#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
按语言合并Parquet数据集
将中文数据合并到一起，英文数据合并到一起
"""

import os
import pandas as pd
import re
from pathlib import Path

def detect_language(text):
    """检测文本语言"""
    if not isinstance(text, str):
        return 'unknown'
    
    # 检测中文字符
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    chinese_chars = len(chinese_pattern.findall(text))
    
    # 检测英文字符
    english_pattern = re.compile(r'[a-zA-Z]+')
    english_chars = len(english_pattern.findall(text))
    
    # 简单的语言判断逻辑
    if chinese_chars > 0:
        return 'zh'
    elif english_chars > 0:
        return 'en'
    else:
        return 'unknown'

def merge_datasets_by_language():
    """按语言合并数据集"""
    
    parquet_dir = "parquet_datasets"
    output_dir = "parquet_datasets/merged_by_language"
    
    print("🔄 开始按语言合并数据集...")
    print(f"扫描目录: {parquet_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 存储不同语言的数据
    chinese_data = []
    english_data = []
    
    # 遍历所有parquet文件
    for root, dirs, files in os.walk(parquet_dir):
        # 跳过输出目录
        if 'merged_by_language' in root:
            continue
            
        for file in files:
            if file.endswith('.parquet'):
                file_path = os.path.join(root, file)
                
                print(f"\n🔄 处理文件: {file_path}")
                
                try:
                    # 读取parquet文件
                    df = pd.read_parquet(file_path)
                    
                    print(f"   数据形状: {df.shape}")
                    print(f"   列名: {list(df.columns)}")
                    
                    # 确保有id列，并处理不同的内容列名
                    if 'id' not in df.columns:
                        print(f"   ⚠️  跳过文件（缺少id列）")
                        continue

                    # 统一列名：将question重命名为prompt
                    if 'question' in df.columns and 'prompt' not in df.columns:
                        df = df.rename(columns={'question': 'prompt'})
                        print(f"   🔄 将question列重命名为prompt")
                    elif 'prompt' not in df.columns:
                        print(f"   ⚠️  跳过文件（缺少prompt或question列）")
                        continue
                    
                    # 添加数据源信息
                    dataset_name = Path(root).name
                    file_name = Path(file).stem
                    df['source'] = f"{dataset_name}_{file_name}"
                    
                    # 根据文件名或内容判断语言
                    if 'zh' in file.lower() or 'chinese' in file.lower():
                        # 明确标记为中文的文件
                        print(f"   📝 识别为中文数据集")
                        chinese_data.append(df)
                        
                    elif 'en' in file.lower() or 'english' in file.lower():
                        # 明确标记为英文的文件
                        print(f"   📝 识别为英文数据集")
                        english_data.append(df)
                        
                    else:
                        # 通过内容检测语言
                        print(f"   🔍 通过内容检测语言...")
                        
                        # 采样一些数据进行语言检测
                        sample_size = min(10, len(df))
                        sample_texts = df['prompt'].head(sample_size).tolist()
                        
                        zh_count = 0
                        en_count = 0
                        
                        for text in sample_texts:
                            lang = detect_language(text)
                            if lang == 'zh':
                                zh_count += 1
                            elif lang == 'en':
                                en_count += 1
                        
                        print(f"   语言检测结果: 中文={zh_count}, 英文={en_count}")
                        
                        if zh_count > en_count:
                            print(f"   📝 识别为中文数据集")
                            chinese_data.append(df)
                        elif en_count > zh_count:
                            print(f"   📝 识别为英文数据集")
                            english_data.append(df)
                        else:
                            print(f"   ⚠️  语言不明确，默认归类为英文")
                            english_data.append(df)
                    
                except Exception as e:
                    print(f"   ❌ 处理文件时出错: {e}")
    
    # 合并中文数据
    if chinese_data:
        print(f"\n🔄 合并中文数据集...")
        chinese_merged = pd.concat(chinese_data, ignore_index=True)
        
        # 重新分配ID
        chinese_merged['id'] = range(len(chinese_merged))
        
        # 重新排列列顺序
        columns = ['id', 'prompt', 'source']
        chinese_merged = chinese_merged[columns]
        
        # 保存中文合并数据
        chinese_output = os.path.join(output_dir, "chinese_merged.parquet")
        chinese_merged.to_parquet(chinese_output, compression='snappy', index=False)
        
        print(f"✅ 中文数据合并完成:")
        print(f"   文件: {chinese_output}")
        print(f"   数据形状: {chinese_merged.shape}")
        print(f"   数据源: {chinese_merged['source'].unique().tolist()}")
        
        # 显示中文数据示例
        print(f"   前3行示例:")
        for i, row in chinese_merged.head(3).iterrows():
            print(f"     ID {row['id']}: {row['prompt'][:60]}... (来源: {row['source']})")
    
    # 合并英文数据
    if english_data:
        print(f"\n🔄 合并英文数据集...")
        english_merged = pd.concat(english_data, ignore_index=True)
        
        # 重新分配ID
        english_merged['id'] = range(len(english_merged))
        
        # 重新排列列顺序
        columns = ['id', 'prompt', 'source']
        english_merged = english_merged[columns]
        
        # 保存英文合并数据
        english_output = os.path.join(output_dir, "english_merged.parquet")
        english_merged.to_parquet(english_output, compression='snappy', index=False)
        
        print(f"✅ 英文数据合并完成:")
        print(f"   文件: {english_output}")
        print(f"   数据形状: {english_merged.shape}")
        print(f"   数据源: {english_merged['source'].unique().tolist()}")
        
        # 显示英文数据示例
        print(f"   前3行示例:")
        for i, row in english_merged.head(3).iterrows():
            print(f"     ID {row['id']}: {row['prompt'][:60]}... (来源: {row['source']})")
    
    # 显示最终统计
    print(f"\n🎉 合并完成!")
    print(f"📊 统计信息:")
    if chinese_data:
        print(f"   中文数据: {len(chinese_merged)} 条记录")
    if english_data:
        print(f"   英文数据: {len(english_merged)} 条记录")
    
    # 显示输出文件
    print(f"\n📁 输出文件:")
    if os.path.exists(output_dir):
        for file in os.listdir(output_dir):
            if file.endswith('.parquet'):
                file_path = os.path.join(output_dir, file)
                size = os.path.getsize(file_path)
                size_str = f"{size/1024:.1f} KB" if size < 1024*1024 else f"{size/1024/1024:.1f} MB"
                print(f"   📄 {file} ({size_str})")

def verify_merged_data():
    """验证合并后的数据"""
    output_dir = "parquet_datasets/merged_by_language"
    
    print(f"\n🔍 验证合并后的数据...")
    
    for file in ['chinese_merged.parquet', 'english_merged.parquet']:
        file_path = os.path.join(output_dir, file)
        
        if os.path.exists(file_path):
            try:
                df = pd.read_parquet(file_path)
                
                print(f"\n📋 {file}:")
                print(f"   数据形状: {df.shape}")
                print(f"   列名: {list(df.columns)}")
                print(f"   数据源分布:")
                
                source_counts = df['source'].value_counts()
                for source, count in source_counts.items():
                    print(f"     {source}: {count} 条")
                
            except Exception as e:
                print(f"❌ 验证 {file} 时出错: {e}")

if __name__ == "__main__":
    # 合并数据集
    merge_datasets_by_language()
    
    # 验证结果
    verify_merged_data()
