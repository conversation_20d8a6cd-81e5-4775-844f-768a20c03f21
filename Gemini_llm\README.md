# Gemini & Google Drive 文件分析器

一个集成了 Google Gemini AI 和 Google Drive 的智能文件分析工具，可以自动分析各种类型的文件并将结果保存到 Google Drive。

## 🌟 主要功能

- **智能文件分析**: 使用 Google Gemini 2.5 Pro 模型分析多种文件格式
- **自动云端保存**: 分析结果自动保存到 Google Drive
- **多格式支持**: 支持图片、文档、代码、音频、视频等多种文件格式
- **批量处理**: 支持单文件和批量文件分析
- **自定义分析**: 可自定义分析提示词和指令
- **交互式界面**: 提供友好的命令行交互界面

## 📋 支持的文件格式

### 图片格式
- PNG, JPEG, JPG, GIF, WebP

### 文档格式
- 纯文本 (.txt)
- CSV 文件 (.csv)
- HTML 文件 (.html)
- JSON 文件 (.json)
- XML 文件 (.xml)
- PDF 文档 (.pdf)

### 代码文件
- Python (.py)
- Java (.java)
- C/C++ (.c, .cpp, .cc, .cxx)
- JavaScript (.js)
- TypeScript (.ts)
- Markdown (.md)

### 音频格式
- WAV, MP3, AAC, OGG, FLAC

### 视频格式
- MP4, MPEG, MOV, AVI, FLV, MPG, WebM, WMV, 3GPP

## 🚀 快速开始

### 1. 环境准备

确保已安装 Python 3.7+，然后安装依赖包：

```bash
pip install google-api-python-client google-auth-httplib2 google-auth-oauthlib google-generativeai python-dotenv
```

### 2. 配置 API 密钥

#### 2.1 获取 Gemini API 密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/app/apikey)
2. 创建新的 API 密钥
3. 复制密钥备用

#### 2.2 配置 Google Drive API

**方式一：使用服务账号（推荐用于自动化）**

1. 访问 [Google Cloud Console](https://console.cloud.google.com/)
2. 创建新项目或选择现有项目
3. 启用 Google Drive API
4. 创建服务账号
5. 下载服务账号密钥文件（JSON格式）
6. 将密钥文件保存到 `credentials/service-account-key.json`

**方式二：使用 OAuth2（推荐用于个人使用）**

1. 在 Google Cloud Console 中创建 OAuth2 客户端ID
2. 下载客户端密钥文件（JSON格式）
3. 将密钥文件保存到 `credentials/client-secrets.json`

### 3. 配置环境变量

编辑 `gem.env` 文件：

```env
# Gemini API 配置
GEMINI_API_KEY=你的_Gemini_API_密钥

# 代理配置（如果需要）
HTTPS_PROXY=http://127.0.0.1:7890
HTTP_PROXY=http://127.0.0.1:7890

# Google Drive API 配置
# 方式1: 使用服务账号密钥文件路径
GOOGLE_SERVICE_ACCOUNT_FILE=credentials/service-account-key.json

# 方式2: 使用OAuth2客户端密钥文件路径
GOOGLE_CLIENT_SECRETS_FILE=credentials/client-secrets.json

# Google Drive 文件夹ID（可选）
GOOGLE_DRIVE_FOLDER_ID=你的文件夹ID
```

### 4. 创建凭据文件夹

```bash
mkdir credentials
```

将下载的 Google API 密钥文件放入 `credentials` 文件夹。

## 📖 使用方法

### 交互式界面（推荐）

运行交互式程序：

```bash
python interactive_analyzer.py
```

按照菜单提示进行操作：
- 选择单文件或批量分析
- 输入文件路径或使用通配符
- 可选择自定义分析提示词
- 自动保存结果到 Google Drive

### 命令行界面

#### 分析单个文件

```bash
python gemini_drive_analyzer.py analyze document.pdf
```

#### 使用自定义提示词

```bash
python gemini_drive_analyzer.py analyze image.jpg --prompt "请详细描述这张图片的内容和风格"
```

#### 批量分析文件

```bash
python gemini_drive_analyzer.py batch *.txt --instructions "重点关注语法错误和改进建议"
```

#### 查看支持的文件格式

```bash
python gemini_drive_analyzer.py formats
```

## 🔧 高级配置

### 自定义分析提示词

可以为不同类型的文件创建专门的分析提示词：

```python
# 代码分析提示词示例
code_prompt = """
请对这段代码进行详细分析，包括：
1. 代码功能和逻辑
2. 代码质量评估
3. 潜在问题和改进建议
4. 性能优化建议
5. 最佳实践建议
"""

# 图片分析提示词示例
image_prompt = """
请详细描述这张图片，包括：
1. 主要内容和场景
2. 色彩和构图分析
3. 风格和技法
4. 情感表达
5. 艺术价值评估
"""
```

### Google Drive 文件夹管理

程序会自动创建按月份命名的分析结果文件夹，格式为 `Gemini_Analysis_YYYYMM`。

你也可以指定特定的文件夹ID：

```bash
python gemini_drive_analyzer.py analyze file.txt --folder-id "你的文件夹ID"
```

## 📁 项目结构

```
Gemini_llm/
├── gem.env                     # 环境配置文件
├── gemini_analyzer.py          # Gemini 分析模块
├── google_drive_manager.py     # Google Drive 管理模块
├── gemini_drive_analyzer.py    # 主程序（命令行版）
├── interactive_analyzer.py     # 交互式程序
├── credentials/                # API 密钥文件夹
│   ├── service-account-key.json
│   └── client-secrets.json
├── README.md                   # 说明文档
└── requirements.txt            # 依赖包列表
```

## 🔍 故障排除

### 常见问题

1. **API 密钥错误**
   - 检查 `gem.env` 文件中的 `GEMINI_API_KEY` 是否正确
   - 确保 API 密钥有效且未过期

2. **Google Drive 认证失败**
   - 检查凭据文件路径是否正确
   - 确保已启用 Google Drive API
   - 对于 OAuth2，首次使用需要在浏览器中完成授权

3. **文件格式不支持**
   - 使用 `python gemini_drive_analyzer.py formats` 查看支持的格式
   - 确保文件扩展名正确

4. **网络连接问题**
   - 检查代理设置
   - 确保能够访问 Google 服务

### 调试模式

如果遇到问题，可以查看详细的错误信息。程序会自动显示错误堆栈跟踪。

## 📄 许可证

本项目仅供学习和研究使用。使用时请遵守相关 API 的使用条款。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 📞 支持

如果遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查环境配置是否正确
3. 确认 API 密钥和权限设置

---

*本工具使用 Google Gemini 2.5 Pro 模型进行文件分析，分析质量取决于模型能力和输入文件质量。*
