"""
Google Drive API 管理模块
支持文件上传、下载和管理功能
"""

import os
import io
import json
from datetime import datetime
from typing import Optional, List, Dict, Any

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from googleapiclient.http import MediaIoBaseUpload, MediaFileUpload
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('gem.env')

class GoogleDriveManager:
    """Google Drive API 管理器"""
    
    # Google Drive API 权限范围
    SCOPES = ['https://www.googleapis.com/auth/drive']
    
    def __init__(self):
        """初始化 Google Drive 管理器"""
        self.service = None
        self.credentials = None
        self._authenticate()
    
    def _authenticate(self):
        """认证 Google Drive API"""
        try:
            # 尝试使用服务账号认证
            service_account_file = os.getenv('GOOGLE_SERVICE_ACCOUNT_FILE')
            if service_account_file and os.path.exists(service_account_file):
                print("🔐 使用服务账号认证...")
                self.credentials = ServiceAccountCredentials.from_service_account_file(
                    service_account_file, scopes=self.SCOPES
                )
            else:
                # 使用 OAuth2 用户认证
                print("🔐 使用 OAuth2 用户认证...")
                self._oauth2_authenticate()
            
            # 构建 Drive API 服务
            self.service = build('drive', 'v3', credentials=self.credentials)
            print("✅ Google Drive API 认证成功！")
            
        except Exception as e:
            print(f"❌ Google Drive API 认证失败: {e}")
            raise
    
    def _oauth2_authenticate(self):
        """OAuth2 用户认证"""
        creds = None
        token_file = 'token.json'
        
        # 检查是否存在已保存的凭据
        if os.path.exists(token_file):
            creds = Credentials.from_authorized_user_file(token_file, self.SCOPES)
        
        # 如果没有有效凭据，进行授权流程
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                creds.refresh(Request())
            else:
                client_secrets_file = os.getenv('GOOGLE_CLIENT_SECRETS_FILE')
                if not client_secrets_file or not os.path.exists(client_secrets_file):
                    raise FileNotFoundError(
                        "未找到 Google OAuth2 客户端密钥文件。请设置 GOOGLE_CLIENT_SECRETS_FILE 环境变量。"
                    )
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    client_secrets_file, self.SCOPES
                )
                creds = flow.run_local_server(port=0)
            
            # 保存凭据以供下次使用
            with open(token_file, 'w') as token:
                token.write(creds.to_json())
        
        self.credentials = creds
    
    def create_folder(self, folder_name: str, parent_folder_id: Optional[str] = None) -> str:
        """
        创建文件夹
        
        Args:
            folder_name: 文件夹名称
            parent_folder_id: 父文件夹ID（可选）
        
        Returns:
            创建的文件夹ID
        """
        try:
            file_metadata = {
                'name': folder_name,
                'mimeType': 'application/vnd.google-apps.folder'
            }
            
            if parent_folder_id:
                file_metadata['parents'] = [parent_folder_id]
            
            folder = self.service.files().create(
                body=file_metadata,
                fields='id'
            ).execute()
            
            folder_id = folder.get('id')
            print(f"✅ 文件夹 '{folder_name}' 创建成功，ID: {folder_id}")
            return folder_id
            
        except HttpError as error:
            print(f"❌ 创建文件夹失败: {error}")
            raise
    
    def upload_text_file(self, content: str, filename: str, 
                        folder_id: Optional[str] = None) -> str:
        """
        上传文本文件到 Google Drive
        
        Args:
            content: 文件内容
            filename: 文件名
            folder_id: 目标文件夹ID（可选）
        
        Returns:
            上传文件的ID
        """
        try:
            # 创建文件元数据
            file_metadata = {'name': filename}
            if folder_id:
                file_metadata['parents'] = [folder_id]
            
            # 创建文件内容
            file_content = io.BytesIO(content.encode('utf-8'))
            media = MediaIoBaseUpload(
                file_content,
                mimetype='text/plain',
                resumable=True
            )
            
            # 上传文件
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink'
            ).execute()
            
            file_id = file.get('id')
            file_name = file.get('name')
            web_link = file.get('webViewLink')
            
            print(f"✅ 文件 '{file_name}' 上传成功")
            print(f"📁 文件ID: {file_id}")
            print(f"🔗 查看链接: {web_link}")
            
            return file_id
            
        except HttpError as error:
            print(f"❌ 上传文件失败: {error}")
            raise
    
    def upload_file(self, file_path: str, folder_id: Optional[str] = None) -> str:
        """
        上传本地文件到 Google Drive
        
        Args:
            file_path: 本地文件路径
            folder_id: 目标文件夹ID（可选）
        
        Returns:
            上传文件的ID
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            filename = os.path.basename(file_path)
            
            # 创建文件元数据
            file_metadata = {'name': filename}
            if folder_id:
                file_metadata['parents'] = [folder_id]
            
            # 创建媒体上传对象
            media = MediaFileUpload(file_path, resumable=True)
            
            # 上传文件
            file = self.service.files().create(
                body=file_metadata,
                media_body=media,
                fields='id,name,webViewLink'
            ).execute()
            
            file_id = file.get('id')
            file_name = file.get('name')
            web_link = file.get('webViewLink')
            
            print(f"✅ 文件 '{file_name}' 上传成功")
            print(f"📁 文件ID: {file_id}")
            print(f"🔗 查看链接: {web_link}")
            
            return file_id
            
        except HttpError as error:
            print(f"❌ 上传文件失败: {error}")
            raise
    
    def list_files(self, folder_id: Optional[str] = None, 
                   max_results: int = 10) -> List[Dict[str, Any]]:
        """
        列出文件
        
        Args:
            folder_id: 文件夹ID（可选）
            max_results: 最大结果数
        
        Returns:
            文件列表
        """
        try:
            query = ""
            if folder_id:
                query = f"'{folder_id}' in parents"
            
            results = self.service.files().list(
                q=query,
                pageSize=max_results,
                fields="nextPageToken, files(id, name, mimeType, createdTime, webViewLink)"
            ).execute()
            
            files = results.get('files', [])
            return files
            
        except HttpError as error:
            print(f"❌ 列出文件失败: {error}")
            raise
    
    def get_default_folder_id(self) -> Optional[str]:
        """获取默认文件夹ID"""
        return os.getenv('GOOGLE_DRIVE_FOLDER_ID')
    
    def ensure_analysis_folder(self) -> str:
        """
        确保分析结果文件夹存在
        
        Returns:
            分析结果文件夹ID
        """
        folder_name = f"Gemini_Analysis_{datetime.now().strftime('%Y%m')}"
        default_folder_id = self.get_default_folder_id()
        
        try:
            # 检查文件夹是否已存在
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder'"
            if default_folder_id:
                query += f" and '{default_folder_id}' in parents"
            
            results = self.service.files().list(q=query).execute()
            files = results.get('files', [])
            
            if files:
                folder_id = files[0]['id']
                print(f"📁 使用现有文件夹: {folder_name}")
            else:
                folder_id = self.create_folder(folder_name, default_folder_id)
                print(f"📁 创建新文件夹: {folder_name}")
            
            return folder_id
            
        except Exception as e:
            print(f"❌ 确保分析文件夹失败: {e}")
            # 如果失败，返回默认文件夹ID或None
            return default_folder_id
