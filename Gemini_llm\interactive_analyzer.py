"""
交互式 Gemini & Google Drive 文件分析器
提供用户友好的交互界面
"""

import os
import sys
from typing import List, Optional
import glob

from gemini_drive_analyzer import GeminiDriveAnalyzer

class InteractiveAnalyzer:
    """交互式分析器"""
    
    def __init__(self):
        """初始化交互式分析器"""
        self.analyzer = GeminiDriveAnalyzer()
        self.current_files = []
    
    def show_welcome(self):
        """显示欢迎信息"""
        print("=" * 70)
        print("🚀 欢迎使用 Gemini & Google Drive 文件分析器!")
        print("=" * 70)
        print("功能说明:")
        print("• 支持多种文件格式的智能分析")
        print("• 自动将分析结果保存到 Google Drive")
        print("• 支持单文件和批量分析")
        print("• 可自定义分析提示词和指令")
        print("=" * 70)
    
    def show_menu(self):
        """显示主菜单"""
        print("\n📋 主菜单:")
        print("1. 分析单个文件")
        print("2. 批量分析文件")
        print("3. 查看支持的文件格式")
        print("4. 设置分析选项")
        print("5. 查看当前设置")
        print("0. 退出程序")
        print("-" * 40)
    
    def get_user_choice(self) -> str:
        """获取用户选择"""
        while True:
            choice = input("请选择操作 (0-5): ").strip()
            if choice in ['0', '1', '2', '3', '4', '5']:
                return choice
            print("❌ 无效选择，请输入 0-5 之间的数字")
    
    def analyze_single_file(self):
        """分析单个文件"""
        print("\n📁 单文件分析")
        print("-" * 30)
        
        # 获取文件路径
        file_path = input("请输入文件路径: ").strip().strip('"\'')
        
        if not file_path:
            print("❌ 文件路径不能为空")
            return
        
        if not os.path.exists(file_path):
            print(f"❌ 文件不存在: {file_path}")
            return
        
        # 检查文件是否支持
        if not self.analyzer.gemini_analyzer.is_supported_file(file_path):
            mime_type = self.analyzer.gemini_analyzer.get_file_mime_type(file_path)
            print(f"❌ 不支持的文件类型: {mime_type}")
            print("💡 使用菜单选项 3 查看支持的文件格式")
            return
        
        # 获取自定义选项
        print("\n⚙️  分析选项 (可选，直接回车使用默认设置):")
        custom_prompt = input("自定义分析提示词: ").strip()
        custom_instructions = input("附加分析指令: ").strip()
        output_filename = input("输出文件名: ").strip()
        
        # 确认分析
        print(f"\n📋 分析配置:")
        print(f"文件: {os.path.basename(file_path)}")
        print(f"提示词: {'自定义' if custom_prompt else '默认'}")
        print(f"附加指令: {'是' if custom_instructions else '否'}")
        print(f"输出文件名: {output_filename if output_filename else '自动生成'}")
        
        confirm = input("\n确认开始分析? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 分析已取消")
            return
        
        # 执行分析
        result = self.analyzer.analyze_and_save(
            file_path=file_path,
            analysis_prompt=custom_prompt if custom_prompt else None,
            custom_instructions=custom_instructions if custom_instructions else None,
            output_filename=output_filename if output_filename else None
        )
        
        if result['success']:
            print(f"\n🎉 分析成功完成!")
            print(f"📄 结果已保存为: {result['output_filename']}")
        else:
            print(f"\n❌ 分析失败: {result.get('error', '未知错误')}")
    
    def analyze_batch_files(self):
        """批量分析文件"""
        print("\n📁 批量文件分析")
        print("-" * 30)
        
        # 获取文件路径
        print("请选择文件添加方式:")
        print("1. 输入文件路径列表 (用空格分隔)")
        print("2. 使用通配符模式 (如 *.txt)")
        print("3. 输入文件夹路径")
        
        method = input("选择方式 (1-3): ").strip()
        
        file_paths = []
        
        if method == '1':
            paths_input = input("请输入文件路径 (用空格分隔): ").strip()
            file_paths = [p.strip().strip('"\'') for p in paths_input.split()]
        
        elif method == '2':
            pattern = input("请输入通配符模式 (如 *.txt, *.jpg): ").strip()
            file_paths = glob.glob(pattern)
        
        elif method == '3':
            folder_path = input("请输入文件夹路径: ").strip().strip('"\'')
            if os.path.isdir(folder_path):
                # 获取文件夹中的所有文件
                for root, dirs, files in os.walk(folder_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        if self.analyzer.gemini_analyzer.is_supported_file(file_path):
                            file_paths.append(file_path)
            else:
                print(f"❌ 文件夹不存在: {folder_path}")
                return
        
        else:
            print("❌ 无效选择")
            return
        
        # 过滤支持的文件
        supported_files = []
        unsupported_files = []
        
        for file_path in file_paths:
            if os.path.exists(file_path) and os.path.isfile(file_path):
                if self.analyzer.gemini_analyzer.is_supported_file(file_path):
                    supported_files.append(file_path)
                else:
                    unsupported_files.append(file_path)
        
        if not supported_files:
            print("❌ 没有找到支持的文件")
            return
        
        print(f"\n📊 文件统计:")
        print(f"✅ 支持的文件: {len(supported_files)} 个")
        if unsupported_files:
            print(f"❌ 不支持的文件: {len(unsupported_files)} 个")
        
        # 显示支持的文件列表
        print(f"\n📋 将要分析的文件:")
        for i, file_path in enumerate(supported_files[:10], 1):  # 最多显示10个
            print(f"  {i}. {os.path.basename(file_path)}")
        
        if len(supported_files) > 10:
            print(f"  ... 还有 {len(supported_files) - 10} 个文件")
        
        # 获取批量分析选项
        print("\n⚙️  批量分析选项 (可选):")
        custom_prompt = input("统一分析提示词: ").strip()
        custom_instructions = input("统一附加指令: ").strip()
        
        # 确认分析
        confirm = input(f"\n确认分析 {len(supported_files)} 个文件? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("❌ 批量分析已取消")
            return
        
        # 执行批量分析
        results = self.analyzer.batch_analyze_and_save(
            file_paths=supported_files,
            analysis_prompt=custom_prompt if custom_prompt else None,
            custom_instructions=custom_instructions if custom_instructions else None
        )
        
        # 显示结果统计
        successful = len([r for r in results if r['success']])
        failed = len(results) - successful
        
        print(f"\n📊 批量分析结果:")
        print(f"✅ 成功: {successful} 个文件")
        print(f"❌ 失败: {failed} 个文件")
        
        if failed > 0:
            print(f"\n❌ 失败的文件:")
            for result in results:
                if not result['success']:
                    print(f"  • {os.path.basename(result['file_path'])}: {result.get('error', '未知错误')}")
    
    def show_supported_formats(self):
        """显示支持的文件格式"""
        print("\n📋 支持的文件格式")
        print("-" * 30)
        self.analyzer.show_supported_formats()
        
        input("\n按回车键返回主菜单...")
    
    def show_current_settings(self):
        """显示当前设置"""
        print("\n⚙️  当前设置")
        print("-" * 30)
        
        # 检查环境变量
        gemini_key = os.getenv("GEMINI_API_KEY")
        service_account = os.getenv("GOOGLE_SERVICE_ACCOUNT_FILE")
        client_secrets = os.getenv("GOOGLE_CLIENT_SECRETS_FILE")
        drive_folder = os.getenv("GOOGLE_DRIVE_FOLDER_ID")
        
        print(f"Gemini API 密钥: {'✅ 已配置' if gemini_key else '❌ 未配置'}")
        print(f"Google 服务账号: {'✅ 已配置' if service_account and os.path.exists(service_account) else '❌ 未配置'}")
        print(f"Google OAuth2 客户端: {'✅ 已配置' if client_secrets and os.path.exists(client_secrets) else '❌ 未配置'}")
        print(f"默认 Drive 文件夹: {'✅ 已配置' if drive_folder else '❌ 未配置'}")
        
        # 检查认证状态
        try:
            if hasattr(self.analyzer.drive_manager, 'service') and self.analyzer.drive_manager.service:
                print("Google Drive 连接: ✅ 已连接")
            else:
                print("Google Drive 连接: ❌ 未连接")
        except:
            print("Google Drive 连接: ❌ 连接失败")
        
        input("\n按回车键返回主菜单...")
    
    def configure_settings(self):
        """配置设置"""
        print("\n⚙️  设置配置")
        print("-" * 30)
        print("当前版本暂不支持在线配置")
        print("请手动编辑 gem.env 文件来配置以下选项:")
        print("• GEMINI_API_KEY - Gemini API 密钥")
        print("• GOOGLE_SERVICE_ACCOUNT_FILE - Google 服务账号密钥文件路径")
        print("• GOOGLE_CLIENT_SECRETS_FILE - Google OAuth2 客户端密钥文件路径")
        print("• GOOGLE_DRIVE_FOLDER_ID - 默认 Google Drive 文件夹ID")
        
        input("\n按回车键返回主菜单...")
    
    def run(self):
        """运行交互式界面"""
        try:
            self.show_welcome()
            
            while True:
                self.show_menu()
                choice = self.get_user_choice()
                
                if choice == '0':
                    print("\n👋 感谢使用 Gemini & Google Drive 文件分析器!")
                    break
                elif choice == '1':
                    self.analyze_single_file()
                elif choice == '2':
                    self.analyze_batch_files()
                elif choice == '3':
                    self.show_supported_formats()
                elif choice == '4':
                    self.configure_settings()
                elif choice == '5':
                    self.show_current_settings()
                
                print()  # 添加空行
        
        except KeyboardInterrupt:
            print("\n\n⚠️  用户中断操作，程序退出")
        except Exception as e:
            print(f"\n❌ 程序运行出错: {e}")
            import traceback
            print(f"详细错误信息:\n{traceback.format_exc()}")


def main():
    """主函数"""
    try:
        app = InteractiveAnalyzer()
        app.run()
    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
