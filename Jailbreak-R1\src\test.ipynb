{"cells": [{"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import json\n", "\n", "with open(\"datasets/Attack_target/Warmup-data/all_attack_target.json\", 'r') as file:\n", "    red_init_data = json.load(file)\n", "red_init_data = red_init_data[:1000]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["with open(\"warmup_attack_target.json\", 'w') as file:\n", "    json.dump(red_init_data, file, indent=4)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1000\n"]}], "source": ["import json\n", "\n", "with open(\"warmup_attack_target.json\", 'r') as file:\n", "    red_init_data = json.load(file)\n", "print(len(red_init_data))"]}], "metadata": {"kernelspec": {"display_name": "torch25", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 2}