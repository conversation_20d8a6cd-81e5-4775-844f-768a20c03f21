#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将JailbreakBench数据集转换为Parquet格式
只保留id和prompt列
"""

import os
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datasets import load_from_disk

def convert_jailbreak_dataset():
    """转换JailbreakBench数据集为Parquet格式"""
    
    # 输入和输出路径
    input_path = "datasets/my_datasets/JailbreakBench_JBB-Behaviors"
    output_dir = "parquet_datasets/JailbreakBench_JBB-Behaviors"
    
    print("🔄 开始转换JailbreakBench数据集...")
    print(f"输入路径: {input_path}")
    print(f"输出路径: {output_dir}")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    try:
        # 加载数据集
        dataset = load_from_disk(input_path)
        print(f"✅ 成功加载数据集")
        print(f"数据集分割: {list(dataset.keys())}")
        
        # 处理每个分割
        for split_name, split_dataset in dataset.items():
            print(f"\n🔄 处理分割: {split_name}")
            
            # 转换为pandas DataFrame
            df = split_dataset.to_pandas()
            
            print(f"   原始形状: {df.shape}")
            print(f"   原始列名: {list(df.columns)}")
            
            # 检查列名并进行映射
            if 'Index' in df.columns and 'Goal' in df.columns:
                # 创建新的DataFrame，只保留需要的列并重命名
                df_new = pd.DataFrame({
                    'id': df['Index'],
                    'prompt': df['Goal']
                })
                
                print(f"   转换后形状: {df_new.shape}")
                print(f"   转换后列名: {list(df_new.columns)}")
                
                # 显示前几行数据
                print(f"   前3行数据:")
                for i, row in df_new.head(3).iterrows():
                    print(f"     ID {row['id']}: {row['prompt'][:80]}...")
                
                # 保存为Parquet文件
                output_file = os.path.join(output_dir, f"{split_name}.parquet")
                df_new.to_parquet(output_file, compression='snappy', index=False)
                
                # 获取文件大小
                file_size = os.path.getsize(output_file)
                size_str = f"{file_size/1024:.1f} KB" if file_size < 1024*1024 else f"{file_size/1024/1024:.1f} MB"
                
                print(f"✅ 成功保存: {output_file}")
                print(f"   文件大小: {size_str}")
                
            else:
                print(f"❌ 未找到预期的列名 'Index' 和 'Goal'")
                print(f"   实际列名: {list(df.columns)}")
        
        print(f"\n🎉 转换完成!")
        
        # 显示最终文件列表
        print(f"\n📁 输出文件:")
        if os.path.exists(output_dir):
            for file in os.listdir(output_dir):
                if file.endswith('.parquet'):
                    file_path = os.path.join(output_dir, file)
                    size = os.path.getsize(file_path)
                    size_str = f"{size/1024:.1f} KB" if size < 1024*1024 else f"{size/1024/1024:.1f} MB"
                    print(f"   📄 {file} ({size_str})")
        
        return output_dir
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return None

def verify_conversion(output_dir):
    """验证转换结果"""
    print(f"\n🔍 验证转换结果...")
    
    for file in os.listdir(output_dir):
        if file.endswith('.parquet'):
            file_path = os.path.join(output_dir, file)
            
            try:
                # 读取Parquet文件
                df = pd.read_parquet(file_path)
                
                print(f"\n📋 文件: {file}")
                print(f"   形状: {df.shape}")
                print(f"   列名: {list(df.columns)}")
                print(f"   数据类型:")
                for col in df.columns:
                    print(f"     {col}: {df[col].dtype}")
                
                print(f"   示例数据:")
                for i, row in df.head(2).iterrows():
                    print(f"     ID {row['id']}: {row['prompt'][:100]}...")
                
            except Exception as e:
                print(f"❌ 验证文件 {file} 时出错: {e}")

if __name__ == "__main__":
    # 转换数据集
    output_dir = convert_jailbreak_dataset()
    
    if output_dir:
        # 验证转换结果
        verify_conversion(output_dir)
        
        print(f"\n✨ 转换完成! 文件保存在: {output_dir}")
    else:
        print(f"\n❌ 转换失败!")
