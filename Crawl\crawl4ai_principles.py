"""
Crawl4AI 爬虫原理演示
展示不同的信息匹配和提取方法
"""
import asyncio
import re
from crawl4ai import AsyncWebCrawler

async def demo_basic_crawling():
    """
    演示基本的网页抓取
    """
    print("=== 基本网页抓取演示 ===")
    
    async with AsyncWebCrawler() as crawler:
        # 1. 最基本的抓取 - 获取完整页面
        result = await crawler.arun(url="https://example.com")
        
        print(f"抓取状态: {'成功' if result.success else '失败'}")
        print(f"内容长度: {len(result.markdown)} 字符")
        print(f"HTML长度: {len(result.html)} 字符")
        print(f"链接数量: {len(result.links) if result.links else 0}")

async def demo_css_selector_targeting():
    """
    演示CSS选择器精确定位
    """
    print("\n=== CSS选择器定位演示 ===")
    
    async with AsyncWebCrawler() as crawler:
        # 2. 使用CSS选择器定位特定内容
        selectors = [
            "article",           # 文章内容
            ".content",          # 类名选择器
            "#main-content",     # ID选择器
            "div.post-content",  # 组合选择器
            "h1, h2, h3",       # 多选择器
            "a[href*='github']", # 属性选择器
        ]
        
        for selector in selectors:
            try:
                result = await crawler.arun(
                    url="https://safetyprompts.com",
                    css_selector=selector
                )
                print(f"选择器 '{selector}': {len(result.markdown)} 字符")
            except Exception as e:
                print(f"选择器 '{selector}' 失败: {e}")

async def demo_content_filtering():
    """
    演示内容过滤和清理
    """
    print("\n=== 内容过滤演示 ===")
    
    async with AsyncWebCrawler() as crawler:
        # 3. 排除不需要的内容
        result = await crawler.arun(
            url="https://safetyprompts.com",
            excluded_tags=['script', 'style', 'nav', 'footer', 'header'],
            only_text=True  # 只提取文本，去除HTML标签
        )
        
        print(f"过滤后内容长度: {len(result.markdown)} 字符")

async def demo_llm_integration():
    """
    演示LLM集成进行智能提取
    """
    print("\n=== LLM智能提取演示 ===")
    
    # 简单的LLM模拟类
    class SimpleLLM:
        async def acreate(self, **kwargs):
            prompt = kwargs.get("prompt", "")
            # 这里可以集成真实的LLM API
            return "LLM处理结果: 提取到的结构化数据"
    
    async with AsyncWebCrawler() as crawler:
        llm = SimpleLLM()
        
        result = await crawler.arun(
            url="https://safetyprompts.com",
            llm=llm,
            prompt="提取所有数据集名称和链接"
        )
        
        print(f"LLM处理结果: {result.markdown}")

def demo_regex_patterns():
    """
    演示正则表达式模式匹配
    """
    print("\n=== 正则表达式模式匹配演示 ===")
    
    # 模拟网页内容
    sample_content = """
    ### HarmEval [[data on GitHub]](https://github.com/example/harmeval) [[paper]](https://arxiv.org/abs/example)
    **550 prompts**. Each prompt is an unsafe question or instruction.
    
    ### ToxiGen [[data on HuggingFace]](https://huggingface.co/datasets/toxigen) 
    **260,851 prompts**. Dataset for implicit hate speech detection.
    """
    
    # 不同的匹配模式
    patterns = {
        "数据集名称": r'###\s+([^[]+)',
        "GitHub链接": r'\[.*?\]\((https://github\.com/[^)]+)\)',
        "HuggingFace链接": r'\[.*?\]\((https://huggingface\.co/[^)]+)\)',
        "提示数量": r'\*\*(\d+(?:,\d+)*)\s+prompts?\*\*',
        "所有链接": r'\[.*?\]\(([^)]+)\)',
        "描述文本": r'\*\*.*?\*\*\.\s*([^.]+\.)'
    }
    
    for name, pattern in patterns.items():
        matches = re.findall(pattern, sample_content)
        print(f"{name}: {matches}")

async def demo_advanced_targeting():
    """
    演示高级定位技术
    """
    print("\n=== 高级定位技术演示 ===")
    
    async with AsyncWebCrawler() as crawler:
        # 4. 组合多种定位方法
        result = await crawler.arun(
            url="https://safetyprompts.com",
            # 等待页面完全加载
            wait_for="networkidle",
            # 设置用户代理
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            # 设置视口大小
            viewport={"width": 1920, "height": 1080},
            # 截图（可选）
            screenshot=True
        )
        
        print(f"高级抓取结果长度: {len(result.markdown)} 字符")

def analyze_website_structure():
    """
    分析网站结构的方法
    """
    print("\n=== 网站结构分析方法 ===")
    
    analysis_methods = {
        "1. 浏览器开发者工具": [
            "F12打开开发者工具",
            "Elements面板查看DOM结构",
            "Network面板分析请求",
            "Console面板测试选择器"
        ],
        
        "2. CSS选择器策略": [
            "类名选择器: .class-name",
            "ID选择器: #element-id", 
            "属性选择器: [data-type='dataset']",
            "层级选择器: div > ul > li",
            "伪类选择器: :nth-child(n)"
        ],
        
        "3. 内容模式识别": [
            "重复结构模式",
            "标题层级结构",
            "链接模式匹配",
            "文本格式规律"
        ],
        
        "4. 动态内容处理": [
            "等待JavaScript渲染",
            "处理AJAX加载内容",
            "模拟用户交互",
            "处理无限滚动"
        ]
    }
    
    for method, techniques in analysis_methods.items():
        print(f"\n{method}:")
        for technique in techniques:
            print(f"  • {technique}")

async def demo_error_handling():
    """
    演示错误处理和重试机制
    """
    print("\n=== 错误处理演示 ===")
    
    async with AsyncWebCrawler() as crawler:
        try:
            # 设置重试和超时
            result = await crawler.arun(
                url="https://safetyprompts.com",
                timeout=30,  # 30秒超时
                retries=3,   # 重试3次
                delay=1      # 重试间隔1秒
            )
            
            # 验证结果
            if result and result.success:
                print("✅ 抓取成功")
            else:
                print("❌ 抓取失败")
                
        except Exception as e:
            print(f"❌ 发生错误: {e}")

async def main():
    """
    主演示函数
    """
    print("Crawl4AI 爬虫原理和信息匹配演示")
    print("=" * 50)
    
    # 运行各种演示
    await demo_basic_crawling()
    await demo_css_selector_targeting()
    await demo_content_filtering()
    await demo_llm_integration()
    demo_regex_patterns()
    await demo_advanced_targeting()
    analyze_website_structure()
    await demo_error_handling()

if __name__ == "__main__":
    asyncio.run(main())
