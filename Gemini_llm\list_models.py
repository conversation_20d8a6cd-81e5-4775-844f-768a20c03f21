import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv('gem.env')

def list_available_models():
    """
    列出所有可用的Gemini模型
    """
    try:
        # 从环境变量中获取API密钥
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            print("错误：请先设置您的 GEMINI_API_KEY 环境变量。")
            return
            
        genai.configure(api_key=api_key)

        print("正在获取可用模型列表...")
        print("=" * 50)
        
        # 获取所有可用模型
        models = genai.list_models()
        
        print(f"共找到 {len(list(models))} 个模型：\n")
        
        # 重新获取模型列表（因为上面的迭代器已经被消耗了）
        models = genai.list_models()
        
        for model in models:
            print(f"模型名称: {model.name}")
            print(f"显示名称: {model.display_name}")
            print(f"描述: {model.description}")
            print(f"支持的方法: {', '.join(model.supported_generation_methods)}")
            print(f"输入token限制: {model.input_token_limit}")
            print(f"输出token限制: {model.output_token_limit}")
            print("-" * 40)
            
    except Exception as e:
        print(f"获取模型列表时出错: {e}")
        print("请检查网络连接和API密钥是否正确。")

if __name__ == "__main__":
    list_available_models()
