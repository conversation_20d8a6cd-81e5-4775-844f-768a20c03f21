import asyncio
import json
import os
import re  # 导入正则表达式模块
import pandas as pd  # 导入pandas用于Excel导出
# 根据用户提供的最新用法，我们导入 AsyncWebCrawler
from crawl4ai import AsyncWebCrawler

# 检查 PyTorch 和 transformers 是否已安装
try:
    import torch
    from transformers import pipeline, AutoTokenizer, AutoModelForCausalLM
except ImportError:
    print("错误：请先安装 PyTorch 和 Transformers。")
    print("运行: pip install torch transformers accelerate")
    exit()

# --- 步骤 1: 创建一个与 Crawl4AI 兼容的本地 LLM 封装类 ---
# (这部分保持不变)
class HuggingFaceLocalLLM:
    """
    一个封装了 Hugging Face transformers pipeline 的类，
    使其能够像一个异步 LLM 一样被 Crawl4AI 调用。
    """
    def __init__(self, model_name="bigscience/bloomz-560m"):
        """
        初始化时加载模型和分词器。
        
        Args:
            model_name (str): 要从 Hugging Face Hub 加载的模型名称。
        """
        print(f"正在加载模型 '{model_name}'... 这可能需要一些时间并且会占用较多内存。")
        
        # 指定模型下载到本地目录
        cache_path = "./models_cache"
        os.makedirs(cache_path, exist_ok=True)
        print(f"模型将下载并缓存到当前目录下的 '{cache_path}' 文件夹中。")

        # 检查是否有可用的 GPU
        device = 0 if torch.cuda.is_available() else -1
        if device == 0:
            print("检测到 GPU，将在 GPU 上运行模型。")
        else:
            print("未检测到 GPU，将在 CPU 上运行模型（速度可能较慢）。")
            
        # 使用 pipeline 加载一个文本生成模型
        self.pipeline = pipeline(
            "text-generation",
            model=model_name,
            model_kwargs={'cache_dir': cache_path},
            device=device,
            torch_dtype=torch.bfloat16 if device == 0 else torch.float32
        )
        print("模型加载完成。")

    async def acreate(self, **kwargs):
        """
        一个异步方法，用于生成文本。Crawl4AI 会调用这个方法。
        """
        prompt = kwargs.get("prompt", "")
        if not prompt:
            return ""

        loop = asyncio.get_running_loop()
        
        try:
            result = await loop.run_in_executor(
                None, self.pipeline, prompt, max_new_tokens=2048, num_return_sequences=1 # 增加生成长度以容纳更多数据集
            )
            generated_text = result[0]['generated_text']
            return generated_text[len(prompt):]
        except Exception as e:
            print(f"模型生成时出错: {e}")
            return "模型处理时发生错误。"


async def main():
    """
    主函数，执行抓取和分析任务。
    """
    print("--- 任务开始 ---")
    
    # --- 步骤 2: 实例化本地 LLM ---
    try:
        local_llm = HuggingFaceLocalLLM()
    except Exception as e:
        print(f"初始化本地 LLM 失败: {e}")
        return

    # --- 步骤 3: 定义基于网页结构分析的全新提示 ---
    prompt_template = """
Extract dataset information from the following text content.

For each dataset mentioned, extract these 4 fields:
1. "dataset_name": The specific name of the dataset
2. "dataset_category": The category it belongs to (like "Broad Safety Datasets", "Bias Datasets", etc.)
3. "link": The external link to the dataset (GitHub, HuggingFace, etc.). Use null if no link found.
4. "description": A brief description of the dataset

Output ONLY a valid JSON array with no additional text or explanations.

Example format:
[
  {
    "dataset_name": "HarmEval",
    "dataset_category": "Broad Safety Datasets",
    "link": "https://github.com/example/harmeval",
    "description": "A benchmark for evaluating safety of language models."
  }
]

Content to analyze:
"""

    # --- 步骤 4: 使用新的 AsyncWebCrawler 语法初始化并运行 Crawl4AI ---
    print("\n正在启动爬虫抓取 SafetyPrompts.com，请稍候...")
    result_text = ""
    async with AsyncWebCrawler() as crawler:
        # 首先尝试不使用CSS选择器，获取完整页面内容
        print("正在抓取网页内容...")
        result = await crawler.arun(
            url="https://safetyprompts.com/"
        )

        # 检查抓取结果
        if result and hasattr(result, 'markdown') and result.markdown:
            print(f"成功抓取到内容，长度: {len(result.markdown)} 字符")

            # 由于小模型可能无法很好地遵循复杂指令，我们直接解析内容
            print("直接解析网页内容...")
            manual_parse_datasets(result.markdown)
            return

            # 备用方案：使用LLM（如果直接解析失败）
            # 将抓取到的内容与提示模板结合
            full_prompt = prompt_template + "\n\n" + result.markdown[:5000]  # 限制长度避免超出模型限制

            print("正在使用本地LLM分析内容...")
            # 直接调用LLM处理内容
            result_text = await local_llm.acreate(prompt=full_prompt)
        else:
            print("抓取网页内容失败")
            return

    # --- 步骤 5: 处理并打印结果 ---
    print("\n--- LLM 返回的原始文本 ---")
    print(result_text[:1000] + "..." if len(result_text) > 1000 else result_text)

    print("\n--- 尝试解析 JSON 并格式化输出 ---")
    try:
        if not result_text:
            print("LLM 没有返回任何内容。")
            return

        # 清理文本，移除可能的前缀和后缀
        cleaned_text = result_text.strip()

        # 尝试多种方式提取JSON
        json_str = None

        # 方法1: 查找完整的JSON数组
        match = re.search(r'\[[\s\S]*\]', cleaned_text)
        if match:
            json_str = match.group(0)
        else:
            # 方法2: 查找JSON对象数组的开始和结束
            start_idx = cleaned_text.find('[')
            if start_idx != -1:
                # 找到最后一个]
                end_idx = cleaned_text.rfind(']')
                if end_idx != -1 and end_idx > start_idx:
                    json_str = cleaned_text[start_idx:end_idx+1]

        if json_str:
            # 尝试修复常见的JSON格式问题
            json_str = json_str.replace('\n', ' ').replace('\r', ' ')
            # 移除多余的空格
            json_str = re.sub(r'\s+', ' ', json_str)

            print(f"提取到的JSON字符串: {json_str[:200]}...")

            data = json.loads(json_str)
            print("解析成功！提取到的数据集信息：")
            for i, dataset in enumerate(data, 1):
                print(f"\n--- 数据集 {i} ---")
                print(f"  数据集名称: {dataset.get('dataset_name', 'N/A')}")
                print(f"  数据集类别: {dataset.get('dataset_category', 'N/A')}")
                print(f"  数据集链接地址: {dataset.get('link', 'N/A')}")
                print(f"  数据集简介: {dataset.get('description', 'N/A')}")

            # 将结果保存到文件
            output_filename = "safetyprompts_datasets.json"
            with open(output_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"\n✅ 结果已成功保存到文件: {output_filename}")

            # 导出到Excel
            export_to_excel(data)

        else:
            print("在返回结果中未找到有效的 JSON 数组。")
            print("尝试手动解析数据集信息...")
            # 备用方案：手动解析数据集信息
            manual_parse_datasets(result_text)

    except json.JSONDecodeError as e:
        print(f"解析 JSON 失败。错误: {e}")
        print("尝试手动解析数据集信息...")
        manual_parse_datasets(result_text)
    except Exception as e:
        print(f"处理结果时发生未知错误: {e}")


def manual_parse_datasets(text):
    """
    手动解析数据集信息的备用方案
    """
    print("\n--- 手动解析数据集信息 ---")

    datasets = []
    current_category = "Unknown"

    lines = text.split('\n')
    for i, line in enumerate(lines):
        # 检查是否是类别标题
        if line.startswith('# ') and not line.startswith('## '):
            current_category = line[2:].strip()
            continue

        # 检查是否是数据集条目
        if line.startswith('### '):
            dataset_info = parse_dataset_line(line, lines[i:i+10])  # 获取后续几行作为描述
            if dataset_info:
                dataset_info['dataset_category'] = current_category
                datasets.append(dataset_info)

    if datasets:
        print(f"手动解析到 {len(datasets)} 个数据集:")
        for i, dataset in enumerate(datasets[:10], 1):  # 只显示前10个
            print(f"\n--- 数据集 {i} ---")
            print(f"  数据集名称: {dataset.get('dataset_name', 'N/A')}")
            print(f"  数据集类别: {dataset.get('dataset_category', 'N/A')}")
            print(f"  数据集链接地址: {dataset.get('link', 'N/A')}")
            print(f"  数据集简介: {dataset.get('description', 'N/A')}")

        # 保存手动解析的结果到JSON
        output_filename = "safetyprompts_datasets_manual.json"
        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(datasets, f, ensure_ascii=False, indent=2)
        print(f"\n✅ 手动解析结果已保存到文件: {output_filename}")

        # 导出到Excel
        export_to_excel(datasets)
    else:
        print("未能解析到数据集信息。")


def parse_dataset_line(line, context_lines):
    """
    解析单个数据集行
    """
    # 提取数据集名称和链接
    name_match = re.search(r'###\s+([^[]+)', line)
    if not name_match:
        return None

    dataset_name = name_match.group(1).strip()

    # 提取所有链接（可能有多个）
    links = []
    link_matches = re.findall(r'\[\[([^\]]+)\]\]\(([^)]+)\)', line)
    for match in link_matches:
        links.append(match[1])  # match[1] 是URL

    # 选择第一个链接作为主链接，如果有的话
    primary_link = links[0] if links else None

    # 提取描述（从后续行中）
    description = ""
    for context_line in context_lines[1:5]:  # 检查后续几行
        if context_line.strip() and not context_line.startswith('#'):
            description = context_line.strip()
            break

    return {
        'dataset_name': dataset_name,
        'link': primary_link,
        'all_links': links,  # 保存所有链接
        'description': description
    }


def export_to_excel(datasets):
    """
    将数据集信息导出到Excel文件，包含格式化
    """
    try:
        print("\n正在导出到Excel文件...")

        # 准备数据用于DataFrame
        excel_data = []
        for dataset in datasets:
            # 处理all_links字段，将列表转换为字符串
            all_links_str = "; ".join(dataset.get('all_links', [])) if dataset.get('all_links') else ""

            excel_data.append({
                '数据集名称': dataset.get('dataset_name', ''),
                '数据集类别': dataset.get('dataset_category', ''),
                '主要链接': dataset.get('link', ''),
                '所有链接': all_links_str,
                '描述': dataset.get('description', '')
            })

        # 创建DataFrame
        df = pd.DataFrame(excel_data)

        # 导出到Excel文件并格式化
        excel_filename = "safetyprompts_datasets.xlsx"

        # 使用ExcelWriter进行更好的格式化
        with pd.ExcelWriter(excel_filename, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='数据集列表', index=False)

            # 获取工作表对象
            worksheet = writer.sheets['数据集列表']

            # 设置列宽
            column_widths = {
                'A': 25,  # 数据集名称
                'B': 20,  # 数据集类别
                'C': 50,  # 主要链接
                'D': 80,  # 所有链接
                'E': 100  # 描述
            }

            for col, width in column_widths.items():
                worksheet.column_dimensions[col].width = width

            # 设置标题行格式
            from openpyxl.styles import Font, PatternFill, Alignment

            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")

            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")

            # 设置文本换行
            for row in worksheet.iter_rows(min_row=2):
                for cell in row:
                    cell.alignment = Alignment(wrap_text=True, vertical="top")

        print(f"✅ 数据已成功导出到Excel文件: {excel_filename}")
        print(f"   总共导出了 {len(datasets)} 个数据集")

        # 显示一些统计信息
        category_counts = df['数据集类别'].value_counts()
        print(f"\n📊 数据集类别统计:")
        for category, count in category_counts.items():
            print(f"   {category}: {count} 个")

        # 创建分类汇总表
        create_category_summary(df, excel_filename)

    except ImportError:
        print("❌ 导出Excel失败: 缺少必要的库")
        print("请安装pandas和openpyxl: pip install pandas openpyxl")
    except Exception as e:
        print(f"❌ 导出Excel时发生错误: {e}")


def create_category_summary(df, excel_filename):
    """
    创建数据集类别汇总表
    """
    try:
        # 创建类别汇总
        category_summary = df.groupby('数据集类别').agg({
            '数据集名称': 'count',
            '主要链接': lambda x: x.notna().sum()
        }).rename(columns={
            '数据集名称': '数据集数量',
            '主要链接': '有链接数量'
        })

        category_summary['有链接比例'] = (category_summary['有链接数量'] / category_summary['数据集数量'] * 100).round(1)
        category_summary = category_summary.reset_index()

        # 添加到Excel文件的新工作表
        with pd.ExcelWriter(excel_filename, engine='openpyxl', mode='a') as writer:
            category_summary.to_excel(writer, sheet_name='类别统计', index=False)

            # 格式化统计表
            worksheet = writer.sheets['类别统计']

            # 设置列宽
            for col in ['A', 'B', 'C', 'D']:
                worksheet.column_dimensions[col].width = 25

            # 设置标题行格式
            from openpyxl.styles import Font, PatternFill, Alignment

            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="70AD47", end_color="70AD47", fill_type="solid")

            for cell in worksheet[1]:
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = Alignment(horizontal="center", vertical="center")

        print(f"✅ 类别统计表已添加到Excel文件")

    except Exception as e:
        print(f"⚠️ 创建类别汇总时发生错误: {e}")





if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
