"""
信息匹配技术演示 - 基于我们项目的实际应用
"""
import re

def demo_safetyprompts_patterns():
    """
    演示SafetyPrompts.com网站的信息匹配模式
    """
    print("=== SafetyPrompts.com 信息匹配演示 ===\n")
    
    # 模拟从网站抓取到的内容
    sample_content = """
# Broad Safety Datasets
55 "Broad Safety" datasets cover several aspects of LLM safety.

### HarmEval [[data on GitHub]](https://github.com/NeuralSentinel/SafeInfer) [[paper on arXiv]](https://arxiv.org/abs/2408.01018)
**550 prompts**. Each prompt is an unsafe question or instruction. HarmEval was created to **evaluate LLM misuse scenarios**. The dataset language is English. Dataset entries are **machine-written** : based on OpenAI and Meta usage policies. The dataset license is not specified. Other notes:

### JBBBehaviours [[data on HuggingFace]](https://huggingface.co/datasets/JailbreakBench/JBB-Behaviors) [[data on GitHub]](https://github.com/JailbreakBench/jailbreakbench) [[paper on arXiv]](https://arxiv.org/abs/2404.01318)
**100 prompts**. Each prompt is an unsafe question or instruction. JBBBehaviours was created to **evaluate effectiveness of different jailbreaking methods**. The dataset language is English.

# Bias Datasets
35 "Bias" datasets evaluate sociodemographic biases in LLMs.

### CrowSPairs [[data on HuggingFace]](https://huggingface.co/datasets/crows_pairs) [[data on GitHub]](https://github.com/nyu-mll/crows-pairs/) [[paper at EMNLP 2020]](https://aclanthology.org/2020.emnlp-main.154)
**1,508 sentence pairs**. Each pair comprises two sentences that are identical except for identity group references.
"""
    
    print("原始内容示例:")
    print("-" * 50)
    print(sample_content[:300] + "...")
    print("-" * 50)
    
    # 1. 类别标题匹配
    print("\n1. 类别标题匹配:")
    category_pattern = r'^#\s+([^#\n]+)$'
    categories = re.findall(category_pattern, sample_content, re.MULTILINE)
    for i, category in enumerate(categories, 1):
        print(f"   类别 {i}: {category.strip()}")
    
    # 2. 数据集名称匹配
    print("\n2. 数据集名称匹配:")
    dataset_pattern = r'###\s+([^[]+)'
    datasets = re.findall(dataset_pattern, sample_content)
    for i, dataset in enumerate(datasets, 1):
        print(f"   数据集 {i}: {dataset.strip()}")
    
    # 3. 链接提取
    print("\n3. 链接提取:")
    link_pattern = r'\[\[([^\]]+)\]\]\(([^)]+)\)'
    links = re.findall(link_pattern, sample_content)
    for i, (text, url) in enumerate(links, 1):
        print(f"   链接 {i}: {text} -> {url}")
    
    # 4. 提示数量提取
    print("\n4. 提示数量提取:")
    prompt_pattern = r'\*\*(\d+(?:,\d+)*)\s+prompts?\*\*'
    prompts = re.findall(prompt_pattern, sample_content)
    for i, count in enumerate(prompts, 1):
        print(f"   数据集 {i}: {count} 个提示")
    
    # 5. 描述文本提取
    print("\n5. 描述文本提取:")
    desc_pattern = r'\*\*\d+[^*]*\*\*\.\s*([^.]+\.)'
    descriptions = re.findall(desc_pattern, sample_content)
    for i, desc in enumerate(descriptions, 1):
        print(f"   描述 {i}: {desc}")

def demo_advanced_parsing():
    """
    演示高级解析技术
    """
    print("\n\n=== 高级解析技术演示 ===\n")
    
    # 模拟复杂的数据集条目
    complex_entry = """
### MultilingualToxicity [[data on HuggingFace]](https://huggingface.co/datasets/unitary/multilingual-toxic-comments) [[paper at ACL 2022]](https://aclanthology.org/2022.acl-long.229/) [[additional data]](https://github.com/unitaryai/multilingual-toxic-comments)
**159,686 comments** across 7 languages. Each comment is labeled for toxicity. MultilingualToxicity was created to **evaluate toxicity detection across multiple languages**. The dataset languages are English, Spanish, Italian, French, Portuguese, Russian, and German. Dataset entries are **human-written** : sampled from Wikipedia talk pages and other sources. The dataset license is CC BY 4.0. Other notes:
  * Covers 7 different languages
  * Includes severity ratings
  * Based on Perspective API annotations
"""
    
    print("复杂条目示例:")
    print("-" * 50)
    print(complex_entry)
    print("-" * 50)
    
    # 解析函数
    def parse_complex_entry(text):
        result = {}
        
        # 提取名称
        name_match = re.search(r'###\s+([^[]+)', text)
        result['name'] = name_match.group(1).strip() if name_match else None
        
        # 提取所有链接
        links = re.findall(r'\[\[([^\]]+)\]\]\(([^)]+)\)', text)
        result['links'] = [{'text': text, 'url': url} for text, url in links]
        
        # 提取数量信息
        count_match = re.search(r'\*\*([^*]+)\*\*', text)
        result['count'] = count_match.group(1) if count_match else None
        
        # 提取语言信息
        lang_match = re.search(r'dataset languages? (?:are|is)\s+([^.]+)', text, re.IGNORECASE)
        result['languages'] = lang_match.group(1) if lang_match else None
        
        # 提取许可证信息
        license_match = re.search(r'dataset license is\s+([^.]+)', text, re.IGNORECASE)
        result['license'] = license_match.group(1) if license_match else None
        
        return result
    
    # 解析结果
    parsed = parse_complex_entry(complex_entry)
    
    print("\n解析结果:")
    print(f"名称: {parsed['name']}")
    print(f"数量: {parsed['count']}")
    print(f"语言: {parsed['languages']}")
    print(f"许可证: {parsed['license']}")
    print("链接:")
    for i, link in enumerate(parsed['links'], 1):
        print(f"  {i}. {link['text']}: {link['url']}")

def demo_error_handling():
    """
    演示错误处理和边界情况
    """
    print("\n\n=== 错误处理演示 ===\n")
    
    # 各种边界情况
    test_cases = [
        "### ValidDataset [[link]](https://example.com)",  # 正常情况
        "### DatasetWithoutLinks",                         # 无链接
        "### ",                                           # 空名称
        "## WrongLevel [[link]](https://example.com)",    # 错误级别
        "### Dataset with [[broken link](incomplete",      # 损坏链接
        "",                                               # 空行
    ]
    
    def safe_parse(text):
        try:
            name_match = re.search(r'###\s+([^[]+)', text)
            if not name_match:
                return None
                
            name = name_match.group(1).strip()
            if not name:
                return None
                
            links = re.findall(r'\[\[([^\]]+)\]\]\(([^)]+)\)', text)
            
            return {
                'name': name,
                'links': [url for _, url in links],
                'link_count': len(links)
            }
        except Exception as e:
            print(f"解析错误: {e}")
            return None
    
    print("测试各种边界情况:")
    for i, case in enumerate(test_cases, 1):
        result = safe_parse(case)
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{i}. {status}: '{case[:30]}{'...' if len(case) > 30 else ''}'")
        if result:
            print(f"   -> 名称: {result['name']}, 链接数: {result['link_count']}")

def demo_performance_optimization():
    """
    演示性能优化技巧
    """
    print("\n\n=== 性能优化技巧 ===\n")
    
    # 大量文本模拟
    large_text = """
# Category 1
### Dataset1 [[link]](https://example1.com)
### Dataset2 [[link]](https://example2.com)
# Category 2
### Dataset3 [[link]](https://example3.com)
""" * 1000  # 模拟大文件
    
    import time
    
    # 方法1: 逐行处理（推荐）
    start_time = time.time()
    lines = large_text.split('\n')
    datasets_method1 = []
    current_category = "Unknown"
    
    for line in lines:
        if line.startswith('# ') and not line.startswith('## '):
            current_category = line[2:].strip()
        elif line.startswith('### '):
            name_match = re.search(r'###\s+([^[]+)', line)
            if name_match:
                datasets_method1.append({
                    'name': name_match.group(1).strip(),
                    'category': current_category
                })
    
    time1 = time.time() - start_time
    
    # 方法2: 全文正则（较慢）
    start_time = time.time()
    all_datasets = re.findall(r'###\s+([^[]+)', large_text)
    datasets_method2 = [{'name': name.strip()} for name in all_datasets]
    time2 = time.time() - start_time
    
    print(f"逐行处理: {len(datasets_method1)} 个数据集, 耗时: {time1:.4f}秒")
    print(f"全文正则: {len(datasets_method2)} 个数据集, 耗时: {time2:.4f}秒")
    print(f"性能提升: {time2/time1:.2f}x")
    
    # 内存优化建议
    print("\n内存优化建议:")
    print("1. 使用生成器而不是列表")
    print("2. 及时释放大变量")
    print("3. 分块处理大文件")
    print("4. 使用编译后的正则表达式")

def main():
    """
    主演示函数
    """
    print("Crawl4AI 信息匹配技术深度演示")
    print("=" * 60)
    
    demo_safetyprompts_patterns()
    demo_advanced_parsing()
    demo_error_handling()
    demo_performance_optimization()
    
    print("\n" + "=" * 60)
    print("演示完成！")

if __name__ == "__main__":
    main()
