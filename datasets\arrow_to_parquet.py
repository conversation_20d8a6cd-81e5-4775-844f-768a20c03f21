#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Arrow文件转Parquet格式工具
支持单个文件转换和批量转换
"""

import os
import sys
import argparse
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from datasets import Dataset, DatasetDict, load_from_disk
from pathlib import Path

class ArrowToParquetConverter:
    def __init__(self, output_dir="./parquet_datasets"):
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)
    
    def convert_arrow_file(self, arrow_file_path, output_parquet_path=None):
        """转换单个Arrow文件为Parquet"""
        print(f"🔄 转换Arrow文件: {arrow_file_path}")
        
        try:
            # 读取Arrow文件
            with pa.ipc.open_file(arrow_file_path) as reader:
                table = reader.read_all()
            
            # 生成输出文件名
            if output_parquet_path is None:
                base_name = Path(arrow_file_path).stem
                output_parquet_path = os.path.join(self.output_dir, f"{base_name}.parquet")
            
            # 保存为Parquet
            pq.write_table(table, output_parquet_path, compression='snappy')
            
            print(f"✅ 转换成功: {output_parquet_path}")
            print(f"   数据形状: {table.shape}")
            print(f"   列名: {table.column_names}")
            
            return output_parquet_path
            
        except Exception as e:
            print(f"❌ 转换失败: {e}")
            return None
    
    def convert_dataset_dict(self, dataset_path, output_name=None):
        """转换Hugging Face数据集目录为Parquet文件"""
        print(f"🔄 转换数据集目录: {dataset_path}")
        
        try:
            # 加载数据集
            dataset = load_from_disk(dataset_path)
            
            # 生成输出目录名
            if output_name is None:
                output_name = Path(dataset_path).name
            
            output_dataset_dir = os.path.join(self.output_dir, output_name)
            os.makedirs(output_dataset_dir, exist_ok=True)
            
            if isinstance(dataset, DatasetDict):
                # 处理多个分割的数据集
                print(f"📊 发现数据集分割: {list(dataset.keys())}")
                
                for split_name, split_dataset in dataset.items():
                    parquet_file = os.path.join(output_dataset_dir, f"{split_name}.parquet")
                    
                    # 转换为Arrow表格
                    table = split_dataset.data.table
                    
                    # 保存为Parquet
                    pq.write_table(table, parquet_file, compression='snappy')
                    
                    print(f"✅ {split_name}: {parquet_file}")
                    print(f"   数据形状: {table.shape}")
                    print(f"   文件大小: {self._get_file_size(parquet_file)}")
                    
            elif isinstance(dataset, Dataset):
                # 处理单个数据集
                parquet_file = os.path.join(output_dataset_dir, "data.parquet")
                
                # 转换为Arrow表格
                table = dataset.data.table
                
                # 保存为Parquet
                pq.write_table(table, parquet_file, compression='snappy')
                
                print(f"✅ 数据集: {parquet_file}")
                print(f"   数据形状: {table.shape}")
                print(f"   文件大小: {self._get_file_size(parquet_file)}")
            
            print(f"🎉 数据集转换完成: {output_dataset_dir}")
            return output_dataset_dir
            
        except Exception as e:
            print(f"❌ 数据集转换失败: {e}")
            return None
    
    def batch_convert_directory(self, input_dir):
        """批量转换目录中的所有Arrow文件和数据集"""
        print(f"🔄 批量转换目录: {input_dir}")
        
        converted_files = []
        
        # 遍历目录查找Arrow文件
        for root, dirs, files in os.walk(input_dir):
            for file in files:
                if file.endswith('.arrow'):
                    file_path = os.path.join(root, file)
                    
                    # 生成相对路径作为输出文件名
                    rel_path = os.path.relpath(file_path, input_dir)
                    output_name = rel_path.replace('.arrow', '.parquet').replace(os.sep, '_')
                    output_path = os.path.join(self.output_dir, output_name)
                    
                    result = self.convert_arrow_file(file_path, output_path)
                    if result:
                        converted_files.append(result)
        
        # 检查是否有数据集目录
        for item in os.listdir(input_dir):
            item_path = os.path.join(input_dir, item)
            if os.path.isdir(item_path):
                # 检查是否是Hugging Face数据集目录
                if any(f in os.listdir(item_path) for f in ['dataset_info.json', 'state.json', 'dataset_dict.json']):
                    result = self.convert_dataset_dict(item_path)
                    if result:
                        converted_files.append(result)
        
        print(f"🎉 批量转换完成，共转换 {len(converted_files)} 个文件/数据集")
        return converted_files
    
    def _get_file_size(self, file_path):
        """获取文件大小的可读格式"""
        try:
            size = os.path.getsize(file_path)
            for unit in ['B', 'KB', 'MB', 'GB']:
                if size < 1024.0:
                    return f"{size:.1f} {unit}"
                size /= 1024.0
            return f"{size:.1f} TB"
        except:
            return "未知"
    
    def show_parquet_info(self, parquet_file_path, rows=5):
        """显示Parquet文件信息和预览"""
        try:
            # 读取Parquet文件信息
            parquet_file = pq.ParquetFile(parquet_file_path)
            
            print(f"\n📋 Parquet文件信息: {parquet_file_path}")
            print(f"文件大小: {self._get_file_size(parquet_file_path)}")
            print(f"行数: {parquet_file.metadata.num_rows}")
            print(f"列数: {parquet_file.metadata.num_columns}")
            print(f"压缩格式: {parquet_file.metadata.row_group(0).column(0).compression}")
            
            # 读取并预览数据
            table = parquet_file.read()
            df = table.to_pandas()
            
            print(f"列名: {list(df.columns)}")
            print(f"\n前 {rows} 行数据:")
            print(df.head(rows).to_string())
            
        except Exception as e:
            print(f"❌ 读取Parquet文件失败: {e}")

def main():
    parser = argparse.ArgumentParser(description='Arrow文件转Parquet工具')
    parser.add_argument('input_path', help='输入文件或目录路径')
    parser.add_argument('--output-dir', default='./parquet_datasets', help='输出目录')
    parser.add_argument('--output-file', help='输出文件名（仅用于单文件转换）')
    parser.add_argument('--info', action='store_true', help='转换后显示文件信息')
    parser.add_argument('--batch', action='store_true', help='批量转换目录中的所有文件')
    
    args = parser.parse_args()
    
    converter = ArrowToParquetConverter(args.output_dir)
    
    if os.path.isfile(args.input_path):
        # 单文件转换
        if not args.input_path.endswith('.arrow'):
            print(f"❌ 不支持的文件格式，仅支持.arrow文件")
            sys.exit(1)
        
        result = converter.convert_arrow_file(args.input_path, args.output_file)
        
        if result and args.info:
            converter.show_parquet_info(result)
            
    elif os.path.isdir(args.input_path):
        # 目录转换
        if args.batch:
            converter.batch_convert_directory(args.input_path)
        else:
            # 尝试作为数据集目录转换
            result = converter.convert_dataset_dict(args.input_path)
            if not result:
                print("❌ 不是有效的数据集目录，尝试使用 --batch 参数进行批量转换")
                sys.exit(1)
    else:
        print(f"❌ 路径不存在: {args.input_path}")
        sys.exit(1)

if __name__ == "__main__":
    # 如果没有命令行参数，提供交互式界面
    if len(sys.argv) == 1:
        print("🔄 Arrow转Parquet工具")
        print("=" * 40)
        
        input_path = input("请输入文件或目录路径: ").strip()
        if not input_path:
            print("❌ 路径不能为空")
            sys.exit(1)
        
        converter = ArrowToParquetConverter()
        
        if os.path.isfile(input_path):
            if not input_path.endswith('.arrow'):
                print(f"❌ 不支持的文件格式，仅支持.arrow文件")
                sys.exit(1)
                
            result = converter.convert_arrow_file(input_path)
            
            if result:
                show_info = input("是否显示转换结果信息？(y/n): ").strip().lower()
                if show_info == 'y':
                    converter.show_parquet_info(result)
                    
        elif os.path.isdir(input_path):
            choice = input("选择转换模式:\n1. 数据集目录转换\n2. 批量文件转换\n请选择 (1/2): ").strip()
            
            if choice == '1':
                converter.convert_dataset_dict(input_path)
            elif choice == '2':
                converter.batch_convert_directory(input_path)
            else:
                print("❌ 无效选择")
        else:
            print(f"❌ 路径不存在: {input_path}")
    else:
        main()
