"""
纯 Gemini 文件分析器（不依赖 Google Drive）
用于测试 Gemini API 功能
"""

import os
import sys
import argparse
from datetime import datetime
from typing import Optional
import traceback

from gemini_analyzer import GeminiAnalyzer

class GeminiOnlyAnalyzer:
    """纯 Gemini 文件分析器"""
    
    def __init__(self):
        """初始化分析器"""
        try:
            print("🚀 初始化 Gemini 文件分析器...")
            self.gemini_analyzer = GeminiAnalyzer()
            print("✅ 初始化完成!")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            sys.exit(1)
    
    def analyze_and_save_local(self, file_path: str, 
                              analysis_prompt: Optional[str] = None,
                              custom_instructions: Optional[str] = None,
                              output_filename: Optional[str] = None) -> dict:
        """
        分析文件并保存结果到本地
        
        Args:
            file_path: 要分析的文件路径
            analysis_prompt: 自定义分析提示词
            custom_instructions: 自定义指令
            output_filename: 输出文件名（可选）
        
        Returns:
            包含分析结果和保存信息的字典
        """
        try:
            print(f"\n{'='*60}")
            print(f"📁 开始分析文件: {os.path.basename(file_path)}")
            print(f"{'='*60}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件是否支持
            if not self.gemini_analyzer.is_supported_file(file_path):
                mime_type = self.gemini_analyzer.get_file_mime_type(file_path)
                raise ValueError(f"不支持的文件类型: {mime_type}")
            
            # 1. 使用 Gemini 分析文件
            print("\n🔍 使用 Gemini 分析文件...")
            analysis_result = self.gemini_analyzer.analyze_file(
                file_path, 
                analysis_prompt, 
                custom_instructions
            )
            
            # 2. 生成分析报告
            print("\n📝 生成分析报告...")
            report = self._generate_analysis_report(
                file_path, 
                analysis_result, 
                analysis_prompt, 
                custom_instructions
            )
            
            # 3. 保存到本地文件
            print("\n💾 保存分析结果到本地...")
            
            # 确定输出文件名
            if not output_filename:
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"Gemini_Analysis_{base_name}_{timestamp}.txt"
            
            # 确保输出目录存在
            output_dir = "analysis_results"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            output_path = os.path.join(output_dir, output_filename)
            
            # 保存分析报告
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            result = {
                'success': True,
                'file_path': file_path,
                'analysis_result': analysis_result,
                'report': report,
                'output_path': output_path,
                'output_filename': output_filename
            }
            
            print(f"\n✅ 分析完成!")
            print(f"📊 分析结果已保存到: {output_path}")
            
            return result
            
        except Exception as e:
            error_msg = f"分析过程失败: {str(e)}"
            print(f"\n❌ {error_msg}")
            print(f"详细错误信息:\n{traceback.format_exc()}")
            
            return {
                'success': False,
                'error': error_msg,
                'file_path': file_path
            }
    
    def _generate_analysis_report(self, file_path: str, analysis_result: str,
                                 analysis_prompt: Optional[str] = None,
                                 custom_instructions: Optional[str] = None) -> str:
        """
        生成完整的分析报告
        
        Args:
            file_path: 文件路径
            analysis_result: Gemini 分析结果
            analysis_prompt: 分析提示词
            custom_instructions: 自定义指令
        
        Returns:
            格式化的分析报告
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        file_info = os.stat(file_path)
        file_size = file_info.st_size
        mime_type = self.gemini_analyzer.get_file_mime_type(file_path)
        
        report = f"""# Gemini 文件分析报告

## 基本信息
- **分析时间**: {timestamp}
- **文件名**: {os.path.basename(file_path)}
- **文件路径**: {file_path}
- **文件大小**: {file_size:,} 字节 ({file_size/1024:.2f} KB)
- **文件类型**: {self.gemini_analyzer.SUPPORTED_MIME_TYPES.get(mime_type, mime_type)}
- **MIME类型**: {mime_type}

## 分析配置
"""
        
        if analysis_prompt:
            report += f"- **自定义分析提示**: {analysis_prompt}\n"
        else:
            report += f"- **分析提示**: 使用默认提示词\n"
        
        if custom_instructions:
            report += f"- **附加指令**: {custom_instructions}\n"
        
        report += f"""
## Gemini 分析结果

{analysis_result}

---

*本报告由 Gemini 文件分析器自动生成*
*分析引擎: Google Gemini 2.5 Pro*
*保存位置: 本地文件系统*
"""
        
        return report
    
    def show_supported_formats(self):
        """显示支持的文件格式"""
        self.gemini_analyzer.show_supported_formats()


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Gemini 文件分析器（本地版）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python gemini_only_analyzer.py analyze document.pdf
  python gemini_only_analyzer.py analyze image.jpg --prompt "请详细描述这张图片"
  python gemini_only_analyzer.py formats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单文件分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析单个文件')
    analyze_parser.add_argument('file', help='要分析的文件路径')
    analyze_parser.add_argument('--prompt', help='自定义分析提示词')
    analyze_parser.add_argument('--instructions', help='自定义指令')
    analyze_parser.add_argument('--output', help='输出文件名')
    
    # 显示支持格式命令
    subparsers.add_parser('formats', help='显示支持的文件格式')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        analyzer = GeminiOnlyAnalyzer()
        
        if args.command == 'analyze':
            result = analyzer.analyze_and_save_local(
                file_path=args.file,
                analysis_prompt=args.prompt,
                custom_instructions=args.instructions,
                output_filename=args.output
            )
            
            if not result['success']:
                sys.exit(1)
        
        elif args.command == 'formats':
            analyzer.show_supported_formats()
    
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        print(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
