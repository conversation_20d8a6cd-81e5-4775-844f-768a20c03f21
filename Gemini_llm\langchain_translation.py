import getpass
import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv('gem.env')

# Lang<PERSON>hain Google GenAI 集成使用 GOOGLE_API_KEY
if not os.environ.get("GOOGLE_API_KEY"):
    # 如果有 GEMINI_API_KEY，使用它
    if os.environ.get("GEMINI_API_KEY"):
        os.environ["GOOGLE_API_KEY"] = os.environ["GEMINI_API_KEY"]
    else:
        os.environ["GOOGLE_API_KEY"] = getpass.getpass("Enter API key for Google Gemini: ")

from langchain.chat_models import init_chat_model
model = init_chat_model("gemini-2.5-pro", model_provider="google_genai")

from langchain_core.prompts import ChatPromptTemplate
system_template = "Just translate the following from {source_language} into {destination_language}"
prompt_template = ChatPromptTemplate.from_messages(
    [("system", system_template), ("user", "{text}")]
)

# 获取用户输入
source_language = input("请输入源语言 (例如: English, Chinese, French): ")
destination_language = input("请输入目标语言 (例如: Chinese, English, Italian): ")
text = input("请输入要翻译的文本: ")

prompt = prompt_template.invoke({"source_language": source_language, "destination_language": destination_language, "text": text})
messages = prompt.to_messages()

response = model.invoke(messages)
print(f"\n翻译结果: {response.content}")

# from langchain_core.messages import HumanMessage, SystemMessage
# messages = [
#     SystemMessage("Translate the following from English into Italian"),
#     HumanMessage("hi!"),
# ]

# print("正在调用 Gemini 模型进行翻译...")
# result = model.invoke(messages)
# print(f"翻译结果: {result.content}")