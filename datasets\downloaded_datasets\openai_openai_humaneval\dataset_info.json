{"builder_name": "parquet", "citation": "", "config_name": "openai_humaneval", "dataset_name": "openai_humaneval", "dataset_size": 194394, "description": "", "download_checksums": {"hf://datasets/openai/openai_humaneval@7dce6050a7d6d172f3cc5c32aa97f52fa1a2e544/openai_humaneval/test-00000-of-00001.parquet": {"num_bytes": 83920, "checksum": null}}, "download_size": 83920, "features": {"task_id": {"dtype": "string", "_type": "Value"}, "prompt": {"dtype": "string", "_type": "Value"}, "canonical_solution": {"dtype": "string", "_type": "Value"}, "test": {"dtype": "string", "_type": "Value"}, "entry_point": {"dtype": "string", "_type": "Value"}}, "homepage": "", "license": "", "size_in_bytes": 278314, "splits": {"test": {"name": "test", "num_bytes": 194394, "num_examples": 164, "dataset_name": "openai_humaneval"}}, "version": {"version_str": "0.0.0", "major": 0, "minor": 0, "patch": 0}}