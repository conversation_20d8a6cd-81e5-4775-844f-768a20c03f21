# SafetyPrompts.com 数据集爬取项目

## 项目概述
本项目成功从 SafetyPrompts.com 网站抓取了 155 个 AI 安全测试数据集的详细信息，并将其导出为结构化的 JSON 和 Excel 格式。

## 文件说明

### 主要文件
- `crawl_datasets.py` - 主要的爬虫脚本，包含完整的抓取和解析功能
- `export_to_excel.py` - 独立的Excel导出脚本
- `safetyprompts_datasets_manual.json` - 抓取到的原始JSON数据
- `safetyprompts_datasets.xlsx` - 格式化的Excel文件（包含两个工作表）

### 输出文件结构

#### JSON文件字段
- `dataset_name`: 数据集名称
- `dataset_category`: 数据集类别
- `link`: 主要链接
- `all_links`: 所有相关链接（数组）
- `description`: 数据集描述

#### Excel文件工作表
1. **数据集列表** - 包含所有155个数据集的详细信息
2. **类别统计** - 按类别汇总的统计信息

## 数据集统计

### 总体统计
- **总数据集数量**: 155个
- **主要类别**: 7个
- **有效链接覆盖率**: 96.8% (150/155)

### 按类别分布
| 数据集类别 | 数量 | 有链接数量 | 有链接比例 |
|-----------|------|-----------|-----------|
| Broad Safety Datasets | 55 | 55 | 100% |
| Bias Datasets | 35 | 35 | 100% |
| Narrow Safety Datasets | 26 | 26 | 100% |
| Value Alignment Datasets | 23 | 23 | 100% |
| Other Datasets | 10 | 10 | 100% |
| Table of Contents | 5 | 0 | 0% |
| Acknowledgements | 1 | 0 | 0% |

## 主要数据集类别说明

### 1. Broad Safety Datasets (55个)
涵盖LLM安全的多个方面，包括：
- **HarmEval**: 550个不安全提示，评估LLM误用场景
- **JBBBehaviours**: 100个不安全提示，评估越狱方法有效性
- **SGBench**: 1,442个恶意指令，评估LLM安全性泛化能力
- **StrongREJECT**: 313个禁止问题，测试越狱技术有效性
- **WildJailbreak**: 261,534个对话，用于训练LLM安全性

### 2. Bias Datasets (35个)
评估LLM中的社会人口偏见，包括：
- **CrowSPairs**: 1,508个句子对，评估社会人口偏见
- **StereoSet**: 16,955个多选题，评估刻板印象偏见
- **BBQ**: 58,492个问答示例，评估问答中的社会偏见
- **BOLD**: 23,679个提示，评估文本生成中的偏见

### 3. Narrow Safety Datasets (26个)
专注于LLM安全的特定方面，包括：
- **AdvBench**: 520个有害行为，评估对抗性提示
- **RealToxicityPrompts**: 99,442个提示，评估毒性内容生成
- **ToxiGen**: 260,851个提示，创建隐式仇恨言论示例

### 4. Value Alignment Datasets (23个)
关注LLM的伦理、道德或社会行为，包括：
- **ETHICS**: 134,420个二选一问题，评估基本伦理知识
- **MoralStories**: 12,000个故事，评估道德推理技能
- **OpinionQA**: 1,498个多选题，评估LLM观点与人群对齐

### 5. Other Datasets (10个)
服务于更专业目的的数据集，包括：
- **HackAPrompt**: 601,757个提示，分析提示攻击
- **ToxicChat**: 10,166个对话，评估对话内容审核

## 技术实现

### 核心技术栈
- **Python 3.x**
- **Crawl4AI**: 网页抓取框架
- **pandas**: 数据处理和Excel导出
- **openpyxl**: Excel格式化
- **正则表达式**: 文本解析

### 主要功能
1. **智能网页抓取**: 使用Crawl4AI获取完整页面内容
2. **结构化解析**: 正则表达式提取数据集信息
3. **多链接支持**: 提取并保存每个数据集的所有相关链接
4. **Excel格式化**: 自动列宽调整、标题格式化、文本换行
5. **统计分析**: 自动生成类别统计和汇总报告

### 解决的技术难题
1. **LLM指令遵循问题**: 放弃小模型生成JSON，改用直接解析
2. **CSS选择器限制**: 获取完整页面内容而非特定元素
3. **多链接提取**: 支持每个数据集的多个相关链接
4. **Excel格式化**: 专业的表格格式和多工作表支持

## 使用方法

### 重新抓取数据
```bash
python crawl_datasets.py
```

### 仅导出Excel（基于现有JSON）
```bash
python export_to_excel.py
```

### 依赖安装
```bash
pip install crawl4ai pandas openpyxl torch transformers
```

## 项目成果

✅ **成功抓取155个AI安全数据集**  
✅ **完整的链接和描述信息**  
✅ **结构化JSON和Excel输出**  
✅ **专业的Excel格式化**  
✅ **自动统计分析**  
✅ **多工作表Excel文件**  

这个项目为AI安全研究提供了一个完整的数据集资源库，便于研究人员快速查找和使用相关数据集。
