"""
Gemini 文件分析模块
支持多种文件格式的上传和分析
"""

import os
import mimetypes
from typing import Optional, Dict, Any, List
from datetime import datetime
import google.generativeai as genai
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('gem.env')

class GeminiAnalyzer:
    """Gemini 文件分析器"""
    
    # 支持的文件类型
    SUPPORTED_MIME_TYPES = {
        # 图片格式
        'image/png': 'PNG图片',
        'image/jpeg': 'JPEG图片',
        'image/jpg': 'JPG图片',
        'image/gif': 'GIF图片',
        'image/webp': 'WebP图片',
        
        # 文档格式
        'text/plain': '纯文本',
        'text/csv': 'CSV文件',
        'text/html': 'HTML文件',
        'text/css': 'CSS文件',
        'text/javascript': 'JavaScript文件',
        'application/json': 'JSON文件',
        'application/xml': 'XML文件',
        
        # 代码文件
        'text/x-python': 'Python代码',
        'text/x-java': 'Java代码',
        'text/x-c': 'C代码',
        'text/x-c++': 'C++代码',
        
        # PDF文档
        'application/pdf': 'PDF文档',
        
        # 音频格式
        'audio/wav': 'WAV音频',
        'audio/mp3': 'MP3音频',
        'audio/aac': 'AAC音频',
        'audio/ogg': 'OGG音频',
        'audio/flac': 'FLAC音频',
        
        # 视频格式
        'video/mp4': 'MP4视频',
        'video/mpeg': 'MPEG视频',
        'video/mov': 'MOV视频',
        'video/avi': 'AVI视频',
        'video/x-flv': 'FLV视频',
        'video/mpg': 'MPG视频',
        'video/webm': 'WebM视频',
        'video/wmv': 'WMV视频',
        'video/3gpp': '3GPP视频'
    }
    
    def __init__(self):
        """初始化 Gemini 分析器"""
        self.api_key = os.getenv("GEMINI_API_KEY")
        if not self.api_key:
            raise ValueError("未找到 GEMINI_API_KEY 环境变量")
        
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel('gemini-2.5-pro')
        print("✅ Gemini 分析器初始化成功")
    
    def get_file_mime_type(self, file_path: str) -> str:
        """
        获取文件的MIME类型
        
        Args:
            file_path: 文件路径
        
        Returns:
            MIME类型字符串
        """
        mime_type, _ = mimetypes.guess_type(file_path)
        if not mime_type:
            # 根据文件扩展名手动判断
            ext = os.path.splitext(file_path)[1].lower()
            ext_to_mime = {
                '.py': 'text/x-python',
                '.java': 'text/x-java',
                '.c': 'text/x-c',
                '.cpp': 'text/x-c++',
                '.cc': 'text/x-c++',
                '.cxx': 'text/x-c++',
                '.js': 'text/javascript',
                '.ts': 'text/typescript',
                '.md': 'text/markdown',
                '.txt': 'text/plain'
            }
            mime_type = ext_to_mime.get(ext, 'application/octet-stream')
        
        return mime_type
    
    def is_supported_file(self, file_path: str) -> bool:
        """
        检查文件是否支持分析
        
        Args:
            file_path: 文件路径
        
        Returns:
            是否支持
        """
        mime_type = self.get_file_mime_type(file_path)
        return mime_type in self.SUPPORTED_MIME_TYPES
    
    def upload_file_to_gemini(self, file_path: str, display_name: Optional[str] = None) -> Any:
        """
        上传文件到 Gemini
        
        Args:
            file_path: 本地文件路径
            display_name: 显示名称（可选）
        
        Returns:
            上传的文件对象
        """
        try:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            if not self.is_supported_file(file_path):
                mime_type = self.get_file_mime_type(file_path)
                raise ValueError(f"不支持的文件类型: {mime_type}")
            
            # 获取文件信息
            file_size = os.path.getsize(file_path)
            mime_type = self.get_file_mime_type(file_path)
            
            if not display_name:
                display_name = os.path.basename(file_path)
            
            print(f"📤 正在上传文件到 Gemini...")
            print(f"   文件: {display_name}")
            print(f"   大小: {file_size / 1024:.2f} KB")
            print(f"   类型: {self.SUPPORTED_MIME_TYPES.get(mime_type, mime_type)}")
            
            # 上传文件到 Gemini
            uploaded_file = genai.upload_file(
                path=file_path,
                display_name=display_name
            )
            
            print(f"✅ 文件上传成功!")
            print(f"   Gemini文件名: {uploaded_file.display_name}")
            print(f"   文件URI: {uploaded_file.uri}")
            
            return uploaded_file
            
        except Exception as e:
            print(f"❌ 文件上传失败: {e}")
            raise
    
    def analyze_file(self, file_path: str, 
                    analysis_prompt: Optional[str] = None,
                    custom_instructions: Optional[str] = None) -> str:
        """
        分析文件内容
        
        Args:
            file_path: 文件路径
            analysis_prompt: 分析提示词（可选）
            custom_instructions: 自定义指令（可选）
        
        Returns:
            分析结果文本
        """
        try:
            # 上传文件到 Gemini
            uploaded_file = self.upload_file_to_gemini(file_path)
            
            # 构建分析提示词
            if not analysis_prompt:
                file_type = self.SUPPORTED_MIME_TYPES.get(
                    self.get_file_mime_type(file_path), 
                    "文件"
                )
                analysis_prompt = self._get_default_analysis_prompt(file_type)
            
            if custom_instructions:
                analysis_prompt = f"{analysis_prompt}\n\n附加要求：{custom_instructions}"
            
            print(f"🔍 正在分析文件...")
            
            # 调用 Gemini 进行分析
            response = self.model.generate_content([
                uploaded_file,
                analysis_prompt
            ])
            
            analysis_result = response.text
            
            print(f"✅ 文件分析完成!")
            print(f"   分析结果长度: {len(analysis_result)} 字符")
            
            # 清理上传的文件（可选）
            try:
                genai.delete_file(uploaded_file.name)
                print(f"🗑️  已清理临时文件: {uploaded_file.display_name}")
            except Exception as cleanup_error:
                print(f"⚠️  清理临时文件失败: {cleanup_error}")
            
            return analysis_result
            
        except Exception as e:
            print(f"❌ 文件分析失败: {e}")
            raise
    
    def _get_default_analysis_prompt(self, file_type: str) -> str:
        """
        获取默认的分析提示词
        
        Args:
            file_type: 文件类型
        
        Returns:
            分析提示词
        """
        base_prompt = f"""请对这个{file_type}进行详细分析，包括以下方面：

1. **内容概述**：简要描述文件的主要内容和目的

2. **结构分析**：分析文件的组织结构和布局

3. **关键信息提取**：提取文件中的重要信息、数据或要点

4. **质量评估**：评估内容的质量、完整性和准确性

5. **改进建议**：提出可能的改进建议或优化方案

6. **总结**：提供简洁的总结和结论

请用中文回答，并保持分析的客观性和专业性。"""

        # 根据文件类型调整提示词
        if "图片" in file_type:
            return base_prompt.replace("内容概述", "图像描述").replace("结构分析", "视觉元素分析")
        elif "代码" in file_type:
            return base_prompt + "\n\n特别关注代码的功能、算法逻辑、代码质量和潜在问题。"
        elif "音频" in file_type:
            return base_prompt.replace("结构分析", "音频特征分析").replace("关键信息提取", "音频内容识别")
        elif "视频" in file_type:
            return base_prompt.replace("结构分析", "视频内容分析").replace("关键信息提取", "关键场景识别")
        elif "PDF" in file_type:
            return base_prompt + "\n\n请特别关注文档的格式、章节结构和核心观点。"
        
        return base_prompt
    
    def batch_analyze_files(self, file_paths: List[str], 
                           analysis_prompt: Optional[str] = None) -> Dict[str, str]:
        """
        批量分析多个文件
        
        Args:
            file_paths: 文件路径列表
            analysis_prompt: 分析提示词（可选）
        
        Returns:
            文件路径到分析结果的映射
        """
        results = {}
        
        print(f"📊 开始批量分析 {len(file_paths)} 个文件...")
        
        for i, file_path in enumerate(file_paths, 1):
            try:
                print(f"\n--- 分析文件 {i}/{len(file_paths)} ---")
                result = self.analyze_file(file_path, analysis_prompt)
                results[file_path] = result
                
            except Exception as e:
                print(f"❌ 分析文件 {file_path} 失败: {e}")
                results[file_path] = f"分析失败: {str(e)}"
        
        print(f"\n✅ 批量分析完成! 成功分析 {len([r for r in results.values() if not r.startswith('分析失败')])} 个文件")
        
        return results
    
    def get_supported_formats(self) -> Dict[str, str]:
        """
        获取支持的文件格式列表
        
        Returns:
            MIME类型到描述的映射
        """
        return self.SUPPORTED_MIME_TYPES.copy()
