"""
简单的 Google Drive 连接测试
"""

import os
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from dotenv import load_dotenv

# 加载环境变量
load_dotenv('gem.env')

SCOPES = ['https://www.googleapis.com/auth/drive']

def test_drive_connection():
    """测试 Google Drive 连接"""
    creds = None
    
    # 检查是否存在已保存的凭据
    if os.path.exists('token.json'):
        creds = Credentials.from_authorized_user_file('token.json', SCOPES)
    
    # 如果没有有效凭据，进行授权流程
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            print("🔄 刷新访问令牌...")
            creds.refresh(Request())
        else:
            client_secrets_file = os.getenv('GOOGLE_CLIENT_SECRETS_FILE')
            if not client_secrets_file:
                print("❌ 未设置 GOOGLE_CLIENT_SECRETS_FILE 环境变量")
                return False
            
            print("🔐 开始 OAuth2 授权流程...")
            flow = InstalledAppFlow.from_client_secrets_file(
                client_secrets_file, SCOPES)
            creds = flow.run_local_server(port=0)
        
        # 保存凭据
        with open('token.json', 'w') as token:
            token.write(creds.to_json())
        print("✅ 凭据已保存")
    
    try:
        # 构建 Drive API 服务
        service = build('drive', 'v3', credentials=creds)
        
        # 测试 API 调用
        results = service.files().list(pageSize=1).execute()
        print("✅ Google Drive API 连接成功！")
        return True
        
    except Exception as e:
        print(f"❌ Google Drive API 连接失败: {e}")
        return False

if __name__ == "__main__":
    test_drive_connection()
