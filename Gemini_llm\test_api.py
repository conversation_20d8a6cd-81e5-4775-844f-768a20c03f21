import google.generativeai as genai
import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv('gem.env')

def test_api():
    """测试API密钥是否有效"""
    try:
        api_key = os.getenv("GEMINI_API_KEY")
        print(f"API密钥: {api_key[:10]}...{api_key[-5:] if api_key else 'None'}")
        
        if not api_key:
            print("❌ 错误：未找到API密钥")
            return False
            
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-2.5-pro')
        
        print("🔄 正在测试API连接...")
        response = model.generate_content("你好，请回复'测试成功'")
        print(f"✅ API测试成功！响应: {response.text}")
        return True
        
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

if __name__ == "__main__":
    test_api()
