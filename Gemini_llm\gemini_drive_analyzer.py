"""
Gemini & Google Drive 文件分析器
主程序 - 整合文件分析和结果保存功能
"""

import os
import sys
import argparse
from datetime import datetime
from typing import List, Optional
import traceback

from gemini_analyzer import GeminiAnalyzer
from google_drive_manager import GoogleDriveManager

class GeminiDriveAnalyzer:
    """Gemini & Google Drive 文件分析器主类"""
    
    def __init__(self):
        """初始化分析器"""
        try:
            print("🚀 初始化 Gemini & Google Drive 文件分析器...")
            self.gemini_analyzer = GeminiAnalyzer()
            self.drive_manager = GoogleDriveManager()
            print("✅ 初始化完成!")
        except Exception as e:
            print(f"❌ 初始化失败: {e}")
            sys.exit(1)
    
    def analyze_and_save(self, file_path: str, 
                        analysis_prompt: Optional[str] = None,
                        custom_instructions: Optional[str] = None,
                        output_filename: Optional[str] = None,
                        drive_folder_id: Optional[str] = None) -> dict:
        """
        分析文件并保存结果到 Google Drive
        
        Args:
            file_path: 要分析的文件路径
            analysis_prompt: 自定义分析提示词
            custom_instructions: 自定义指令
            output_filename: 输出文件名（可选）
            drive_folder_id: Google Drive 文件夹ID（可选）
        
        Returns:
            包含分析结果和保存信息的字典
        """
        try:
            print(f"\n{'='*60}")
            print(f"📁 开始分析文件: {os.path.basename(file_path)}")
            print(f"{'='*60}")
            
            # 检查文件是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件不存在: {file_path}")
            
            # 检查文件是否支持
            if not self.gemini_analyzer.is_supported_file(file_path):
                mime_type = self.gemini_analyzer.get_file_mime_type(file_path)
                raise ValueError(f"不支持的文件类型: {mime_type}")
            
            # 1. 使用 Gemini 分析文件
            print("\n🔍 第一步: 使用 Gemini 分析文件...")
            analysis_result = self.gemini_analyzer.analyze_file(
                file_path, 
                analysis_prompt, 
                custom_instructions
            )
            
            # 2. 生成分析报告
            print("\n📝 第二步: 生成分析报告...")
            report = self._generate_analysis_report(
                file_path, 
                analysis_result, 
                analysis_prompt, 
                custom_instructions
            )
            
            # 3. 保存到 Google Drive
            print("\n☁️  第三步: 保存分析结果到 Google Drive...")
            
            # 确定输出文件名
            if not output_filename:
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_filename = f"Gemini_Analysis_{base_name}_{timestamp}.txt"
            
            # 确定保存文件夹
            if not drive_folder_id:
                drive_folder_id = self.drive_manager.ensure_analysis_folder()
            
            # 上传分析报告
            file_id = self.drive_manager.upload_text_file(
                content=report,
                filename=output_filename,
                folder_id=drive_folder_id
            )
            
            result = {
                'success': True,
                'file_path': file_path,
                'analysis_result': analysis_result,
                'report': report,
                'drive_file_id': file_id,
                'output_filename': output_filename,
                'drive_folder_id': drive_folder_id
            }
            
            print(f"\n✅ 分析完成!")
            print(f"📊 分析结果已保存到 Google Drive")
            print(f"📄 文件名: {output_filename}")
            
            return result
            
        except Exception as e:
            error_msg = f"分析过程失败: {str(e)}"
            print(f"\n❌ {error_msg}")
            print(f"详细错误信息:\n{traceback.format_exc()}")
            
            return {
                'success': False,
                'error': error_msg,
                'file_path': file_path
            }
    
    def batch_analyze_and_save(self, file_paths: List[str],
                              analysis_prompt: Optional[str] = None,
                              custom_instructions: Optional[str] = None,
                              drive_folder_id: Optional[str] = None) -> List[dict]:
        """
        批量分析文件并保存结果
        
        Args:
            file_paths: 文件路径列表
            analysis_prompt: 自定义分析提示词
            custom_instructions: 自定义指令
            drive_folder_id: Google Drive 文件夹ID（可选）
        
        Returns:
            分析结果列表
        """
        print(f"\n🚀 开始批量分析 {len(file_paths)} 个文件...")
        
        results = []
        successful_count = 0
        
        for i, file_path in enumerate(file_paths, 1):
            print(f"\n--- 处理文件 {i}/{len(file_paths)} ---")
            
            result = self.analyze_and_save(
                file_path=file_path,
                analysis_prompt=analysis_prompt,
                custom_instructions=custom_instructions,
                drive_folder_id=drive_folder_id
            )
            
            results.append(result)
            
            if result['success']:
                successful_count += 1
        
        print(f"\n{'='*60}")
        print(f"📊 批量分析完成!")
        print(f"✅ 成功: {successful_count}/{len(file_paths)} 个文件")
        print(f"❌ 失败: {len(file_paths) - successful_count}/{len(file_paths)} 个文件")
        print(f"{'='*60}")
        
        return results
    
    def _generate_analysis_report(self, file_path: str, analysis_result: str,
                                 analysis_prompt: Optional[str] = None,
                                 custom_instructions: Optional[str] = None) -> str:
        """
        生成完整的分析报告
        
        Args:
            file_path: 文件路径
            analysis_result: Gemini 分析结果
            analysis_prompt: 分析提示词
            custom_instructions: 自定义指令
        
        Returns:
            格式化的分析报告
        """
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        file_info = os.stat(file_path)
        file_size = file_info.st_size
        mime_type = self.gemini_analyzer.get_file_mime_type(file_path)
        
        report = f"""# Gemini 文件分析报告

## 基本信息
- **分析时间**: {timestamp}
- **文件名**: {os.path.basename(file_path)}
- **文件路径**: {file_path}
- **文件大小**: {file_size:,} 字节 ({file_size/1024:.2f} KB)
- **文件类型**: {self.gemini_analyzer.SUPPORTED_MIME_TYPES.get(mime_type, mime_type)}
- **MIME类型**: {mime_type}

## 分析配置
"""
        
        if analysis_prompt:
            report += f"- **自定义分析提示**: {analysis_prompt}\n"
        else:
            report += f"- **分析提示**: 使用默认提示词\n"
        
        if custom_instructions:
            report += f"- **附加指令**: {custom_instructions}\n"
        
        report += f"""
## Gemini 分析结果

{analysis_result}

---

*本报告由 Gemini & Google Drive 文件分析器自动生成*
*分析引擎: Google Gemini 2.5 Pro*
*存储服务: Google Drive*
"""
        
        return report
    
    def show_supported_formats(self):
        """显示支持的文件格式"""
        formats = self.gemini_analyzer.get_supported_formats()
        
        print("\n📋 支持的文件格式:")
        print("=" * 50)
        
        categories = {
            '图片格式': [],
            '文档格式': [],
            '代码文件': [],
            '音频格式': [],
            '视频格式': [],
            '其他格式': []
        }
        
        for mime_type, description in formats.items():
            if 'image/' in mime_type:
                categories['图片格式'].append(description)
            elif any(x in mime_type for x in ['text/', 'application/json', 'application/xml', 'application/pdf']):
                categories['文档格式'].append(description)
            elif 'text/x-' in mime_type:
                categories['代码文件'].append(description)
            elif 'audio/' in mime_type:
                categories['音频格式'].append(description)
            elif 'video/' in mime_type:
                categories['视频格式'].append(description)
            else:
                categories['其他格式'].append(description)
        
        for category, items in categories.items():
            if items:
                print(f"\n{category}:")
                for item in sorted(items):
                    print(f"  • {item}")
        
        print(f"\n总计支持 {len(formats)} 种文件格式")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="Gemini & Google Drive 文件分析器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python gemini_drive_analyzer.py analyze document.pdf
  python gemini_drive_analyzer.py analyze image.jpg --prompt "请详细描述这张图片"
  python gemini_drive_analyzer.py batch *.txt --instructions "重点关注语法错误"
  python gemini_drive_analyzer.py formats
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 单文件分析命令
    analyze_parser = subparsers.add_parser('analyze', help='分析单个文件')
    analyze_parser.add_argument('file', help='要分析的文件路径')
    analyze_parser.add_argument('--prompt', help='自定义分析提示词')
    analyze_parser.add_argument('--instructions', help='自定义指令')
    analyze_parser.add_argument('--output', help='输出文件名')
    analyze_parser.add_argument('--folder-id', help='Google Drive 文件夹ID')
    
    # 批量分析命令
    batch_parser = subparsers.add_parser('batch', help='批量分析多个文件')
    batch_parser.add_argument('files', nargs='+', help='要分析的文件路径列表')
    batch_parser.add_argument('--prompt', help='自定义分析提示词')
    batch_parser.add_argument('--instructions', help='自定义指令')
    batch_parser.add_argument('--folder-id', help='Google Drive 文件夹ID')
    
    # 显示支持格式命令
    subparsers.add_parser('formats', help='显示支持的文件格式')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    try:
        analyzer = GeminiDriveAnalyzer()
        
        if args.command == 'analyze':
            result = analyzer.analyze_and_save(
                file_path=args.file,
                analysis_prompt=args.prompt,
                custom_instructions=args.instructions,
                output_filename=args.output,
                drive_folder_id=args.folder_id
            )
            
            if not result['success']:
                sys.exit(1)
        
        elif args.command == 'batch':
            results = analyzer.batch_analyze_and_save(
                file_paths=args.files,
                analysis_prompt=args.prompt,
                custom_instructions=args.instructions,
                drive_folder_id=args.folder_id
            )
            
            # 检查是否有失败的文件
            failed_count = len([r for r in results if not r['success']])
            if failed_count > 0:
                sys.exit(1)
        
        elif args.command == 'formats':
            analyzer.show_supported_formats()
    
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        print(f"详细错误信息:\n{traceback.format_exc()}")
        sys.exit(1)


if __name__ == "__main__":
    main()
