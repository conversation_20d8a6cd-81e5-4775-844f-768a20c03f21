# 通用数据集下载工具

一个简洁的Python脚本，支持从Hugging Face下载任何数据集，提供三种下载方法以应对不同的网络环境。

## 特性

- 🚀 **三种下载方法**：Git克隆、Hugging Face官方API、镜像站点
- 🔄 **自动重试**：如果一种方法失败，自动尝试下一种
- 📦 **通用适配**：支持所有Hugging Face数据集
- 🛠️ **命令行友好**：支持命令行参数和脚本调用

## 安装依赖

```bash
pip install datasets
```

## 使用方法

### 1. 直接运行（默认下载 deepmind/code_contests）

```bash
python download.py
```

### 2. 命令行指定数据集

```bash
# 基本用法
python download.py deepmind/code_contests

# 指定保存路径
python download.py deepmind/code_contests --save-path ./my_datasets

# 只使用特定方法
python download.py deepmind/code_contests --methods git huggingface

# 下载特定分割
python download.py squad --split train

# 下载特定配置
python download.py glue --name cola
```

### 3. 在代码中使用

```python
from download import DatasetDownloader

# 创建下载器
downloader = DatasetDownloader("deepmind/code_contests", "./my_datasets")

# 下载数据集
success = downloader.download()

# 或指定特定方法
success = downloader.download(methods=['mirror', 'git'])
```

## 下载方法说明

1. **Git方法**：直接克隆整个仓库，包含所有文件和版本历史
2. **Hugging Face方法**：使用官方datasets库，只下载数据文件
3. **镜像方法**：使用hf-mirror.com镜像站点，适合网络受限环境

## 常见问题

- **网络连接问题**：脚本会自动尝试三种方法
- **权限问题**：某些数据集需要Hugging Face账户授权
- **存储空间**：大型数据集可能需要几GB空间

## 支持的数据集示例

- `deepmind/code_contests` - 编程竞赛数据集
- `squad` - 阅读理解数据集  
- `glue` - 自然语言理解基准
- `imdb` - 电影评论情感分析
- `wikitext` - 维基百科文本数据
- 以及所有其他Hugging Face数据集...
